using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using FactoryManagementSystem.Helpers;

namespace FactoryManagementSystem.Views
{
    public partial class OrdersReportWindow : Window
    {
        private List<OrderViewModel> _orders;

        public OrdersReportWindow(List<OrderViewModel> orders)
        {
            InitializeComponent();
            _orders = orders ?? new List<OrderViewModel>();
            GenerateReport();
        }

        private void GenerateReport()
        {
            try
            {
                // معلومات التقرير
                TxtReportDate.Text = $"تاريخ التقرير: {EgyptTimeHelper.Now:yyyy/MM/dd HH:mm}";
                
                if (_orders.Any())
                {
                    var minDate = _orders.Min(o => o.OrderDate);
                    var maxDate = _orders.Max(o => o.OrderDate);
                    TxtReportPeriod.Text = $"فترة التقرير: من {minDate:yyyy/MM/dd} إلى {maxDate:yyyy/MM/dd}";
                }
                else
                {
                    TxtReportPeriod.Text = "فترة التقرير: لا توجد بيانات";
                }

                TxtTotalRecords.Text = $"إجمالي السجلات: {_orders.Count}";

                // الملخص المالي
                var totalAmount = _orders.Sum(o => o.TotalAmount);
                var totalPaid = _orders.Sum(o => o.PaidAmount);
                var totalRemaining = _orders.Sum(o => o.RemainingAmount);

                TxtTotalAmount.Text = $"إجمالي المبالغ: {totalAmount:N0} ج.م";
                TxtTotalPaid.Text = $"إجمالي المدفوع: {totalPaid:N0} ج.م";
                TxtTotalRemaining.Text = $"إجمالي المتبقي: {totalRemaining:N0} ج.م";

                // إحصائيات حسب النوع
                var customerOrders = _orders.Where(o => o.OrderType == "CustomerOrder").ToList();
                var invoices = _orders.Where(o => o.OrderType == "Invoice").ToList();
                var purchases = _orders.Where(o => o.OrderType == "Purchase").ToList();

                TxtCustomerOrdersCount.Text = $"العدد: {customerOrders.Count}";
                TxtCustomerOrdersAmount.Text = $"المبلغ: {customerOrders.Sum(o => o.TotalAmount):N0} ج.م";

                TxtInvoicesCount.Text = $"العدد: {invoices.Count}";
                TxtInvoicesAmount.Text = $"المبلغ: {invoices.Sum(o => o.TotalAmount):N0} ج.م";

                TxtPurchasesCount.Text = $"العدد: {purchases.Count}";
                TxtPurchasesAmount.Text = $"المبلغ: {purchases.Sum(o => o.TotalAmount):N0} ج.م";

                // إحصائيات حسب الحالة
                TxtPendingCount.Text = _orders.Count(o => o.Status == "Pending").ToString();
                TxtInProgressCount.Text = _orders.Count(o => o.Status == "InProgress").ToString();
                TxtCompletedCount.Text = _orders.Count(o => o.Status == "Completed" || o.Status == "Paid").ToString();
                TxtCancelledCount.Text = _orders.Count(o => o.Status == "Cancelled").ToString();

                // أعلى العملاء والموردين
                GenerateTopCustomersAndSuppliers();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateTopCustomersAndSuppliers()
        {
            try
            {
                // أعلى العملاء (من طلبات العملاء والفواتير)
                var customerData = _orders
                    .Where(o => o.OrderType == "CustomerOrder" || o.OrderType == "Invoice")
                    .GroupBy(o => o.PartyName)
                    .Select(g => new TopPartyViewModel
                    {
                        Name = g.Key,
                        Amount = g.Sum(o => o.TotalAmount)
                    })
                    .OrderByDescending(x => x.Amount)
                    .Take(5)
                    .ToList();

                DgTopCustomers.ItemsSource = customerData;

                // أعلى الموردين (من طلبات الشراء)
                var supplierData = _orders
                    .Where(o => o.OrderType == "Purchase")
                    .GroupBy(o => o.PartyName)
                    .Select(g => new TopPartyViewModel
                    {
                        Name = g.Key,
                        Amount = g.Sum(o => o.TotalAmount)
                    })
                    .OrderByDescending(x => x.Amount)
                    .Take(5)
                    .ToList();

                DgTopSuppliers.ItemsSource = supplierData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء قوائم أعلى العملاء والموردين: {ex.Message}");
            }
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // إنشاء نافذة معاينة الطباعة
                    var printWindow = new OrdersPrintPreviewWindow(_orders);
                    printWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_orders == null || !_orders.Any())
                {
                    MessageBox.Show("لا توجد طلبات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var fileName = Helpers.ExcelExportHelper.ShowSaveDialog(
                    $"تقرير_الطلبات_المفصل_{EgyptTimeHelper.Now:yyyyMMdd_HHmmss}.csv",
                    "تصدير تقرير الطلبات");

                if (!string.IsNullOrEmpty(fileName))
                {
                    var headers = new Dictionary<string, Func<OrderViewModel, object>>
                    {
                        { "الملاحظات", o => o.Notes ?? "-" },
                        { "المتبقي", o => $"{o.RemainingAmount:N2} ج.م" },
                        { "المدفوع", o => $"{o.PaidAmount:N2} ج.م" },
                        { "الإجمالي", o => $"{o.TotalAmount:N2} ج.م" },
                        { "الحالة", o => o.StatusText ?? "-" },
                        { "التاريخ", o => o.OrderDate.ToString("yyyy/MM/dd") },
                        { "العميل/المورد", o => o.PartyName ?? "-" },
                        { "النوع", o => o.OrderTypeText ?? "-" },
                        { "رقم الطلب", o => o.OrderNumber ?? "-" }
                    };

                    var summary = new Dictionary<string, decimal>
                    {
                        { "إجمالي السجلات", _orders.Count() },
                        { "إجمالي المبالغ", _orders.Sum(o => o.TotalAmount) },
                        { "إجمالي المدفوع", _orders.Sum(o => o.PaidAmount) },
                        { "إجمالي المتبقي", _orders.Sum(o => o.RemainingAmount) }
                    };

                    Helpers.ExcelExportHelper.ExportFinancialReport(
                        _orders,
                        headers,
                        fileName,
                        "تقرير الطلبات والفواتير المفصل",
                        summary);

                    MessageBox.Show($"تم تصدير {_orders.Count()} طلب بنجاح إلى:\n{fileName}",
                        "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }

    public class TopPartyViewModel
    {
        public string Name { get; set; } = string.Empty;
        public decimal Amount { get; set; }
    }
}
