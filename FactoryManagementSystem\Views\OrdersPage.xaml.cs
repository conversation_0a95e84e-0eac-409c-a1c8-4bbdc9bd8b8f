using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;
using Microsoft.EntityFrameworkCore;

namespace FactoryManagementSystem.Views
{
    public partial class OrdersPage : Page
    {
        private List<OrderViewModel> _allOrders = new();

        public OrdersPage()
        {
            try
            {
                InitializeComponent();

                // تأخير تحميل البيانات لتجنب مشاكل التهيئة
                Loaded += OrdersPage_Loaded;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة صفحة الطلبات:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OrdersPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                LoadOrders();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل صفحة الطلبات:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadOrders()
        {
            try
            {
                // تهيئة قائمة فارغة
                _allOrders = new List<OrderViewModel>();

                // التحقق من وجود DataGrid قبل التعيين
                if (DgOrders != null)
                {
                    DgOrders.ItemsSource = _allOrders;
                    DgOrders.SelectionChanged += DgOrders_SelectionChanged;
                }

                // تحميل البيانات الحقيقية
                LoadRealDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات:\n{ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadRealDataAsync()
        {
            try
            {
                using var context = new FactoryDbContext();
                var allOrders = new List<OrderViewModel>();

                // تحميل طلبات العملاء
                try
                {
                    var customerOrders = await context.CustomerOrders
                        .Include(o => o.Customer)
                        .ToListAsync();

                    foreach (var order in customerOrders)
                    {
                        allOrders.Add(new OrderViewModel
                        {
                            OrderId = order.OrderId,
                            OrderNumber = order.OrderNumber,
                            OrderType = "CustomerOrder",
                            OrderTypeText = "طلب عميل",
                            PartyName = order.Customer?.Name ?? "عميل غير محدد",
                            OrderDate = order.OrderDate,
                            Status = order.Status.ToString(),
                            StatusText = GetCustomerOrderStatusText(order.Status),
                            TotalAmount = order.TotalAmount,
                            PaidAmount = order.PaidAmount,
                            RemainingAmount = order.RemainingAmount,
                            Notes = order.Notes ?? "",
                            CustomerId = order.CustomerId,
                            SupplierId = null
                        });
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل طلبات العملاء: {ex.Message}");
                }

                // تحميل الفواتير
                try
                {
                    var invoices = await context.Invoices
                        .Include(i => i.Customer)
                        .ToListAsync();

                    foreach (var invoice in invoices)
                    {
                        allOrders.Add(new OrderViewModel
                        {
                            OrderId = invoice.InvoiceId,
                            OrderNumber = invoice.InvoiceNumber,
                            OrderType = "Invoice",
                            OrderTypeText = "فاتورة مبيعات",
                            PartyName = invoice.Customer?.Name ?? "عميل غير محدد",
                            OrderDate = invoice.InvoiceDate,
                            Status = invoice.Status.ToString(),
                            StatusText = GetInvoiceStatusText(invoice.Status),
                            TotalAmount = invoice.TotalAmount,
                            PaidAmount = invoice.PaidAmount,
                            RemainingAmount = invoice.TotalAmount - invoice.PaidAmount,
                            Notes = invoice.Notes ?? "",
                            CustomerId = invoice.CustomerId,
                            SupplierId = invoice.SupplierId
                        });
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الفواتير: {ex.Message}");
                }

                // تحميل المشتريات
                try
                {
                    var purchases = await context.Purchases
                        .Include(p => p.Supplier)
                        .ToListAsync();

                    foreach (var purchase in purchases)
                    {
                        allOrders.Add(new OrderViewModel
                        {
                            OrderId = purchase.PurchaseId,
                            OrderNumber = purchase.PurchaseNumber,
                            OrderType = "Purchase",
                            OrderTypeText = "طلب شراء",
                            PartyName = purchase.Supplier?.Name ?? "مورد غير محدد",
                            OrderDate = purchase.PurchaseDate,
                            Status = purchase.Status.ToString(),
                            StatusText = GetPurchaseStatusText(purchase.Status),
                            TotalAmount = purchase.TotalAmount,
                            PaidAmount = purchase.PaidAmount,
                            RemainingAmount = purchase.RemainingAmount,
                            Notes = purchase.Notes ?? "",
                            CustomerId = null,
                            SupplierId = purchase.SupplierId
                        });
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تحميل طلبات الشراء: {ex.Message}");
                }



                // تحديث القائمة
                _allOrders = allOrders.OrderByDescending(o => o.OrderDate).ToList();
                if (DgOrders != null)
                {
                    DgOrders.ItemsSource = _allOrders;
                }

                // تحديث الإحصائيات
                UpdateOrdersCount();

                // تحديث وقت آخر تحديث
                if (TxtLastUpdate != null)
                {
                    TxtLastUpdate.Text = EgyptTimeHelper.Now.ToString("HH:mm:ss");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل البيانات الحقيقية: {ex.Message}");
            }
        }

        private void UpdateOrdersCount()
        {
            if (_allOrders != null)
            {
                var totalOrders = _allOrders.Count;
                var customerOrders = _allOrders.Count(o => o.OrderType == "CustomerOrder");
                var invoices = _allOrders.Count(o => o.OrderType == "Invoice");
                var purchases = _allOrders.Count(o => o.OrderType == "Purchase");

                var totalPaid = _allOrders.Sum(o => o.PaidAmount);
                var totalRemaining = _allOrders.Sum(o => o.RemainingAmount);

                // تحديث نص الإحصائيات
                if (TxtStatistics != null)
                {
                    TxtStatistics.Text = $"إجمالي: {totalOrders} | طلبات عملاء: {customerOrders} | فواتير: {invoices} | مشتريات: {purchases}";
                }

                // تحديث ملخص المدفوعات
                if (TxtPaymentSummary != null)
                {
                    TxtPaymentSummary.Text = $"مدفوع: {totalPaid:N0} ج.م | متبقي: {totalRemaining:N0} ج.م";
                }
            }
        }



        private string GetCustomerOrderStatusText(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.Pending => "معلق",
                OrderStatus.InProgress => "جاري التنفيذ",
                OrderStatus.PartiallyPaid => "مدفوع جزئياً",
                OrderStatus.Completed => "مكتمل",
                OrderStatus.Delivered => "تم التسليم",
                OrderStatus.Cancelled => "ملغي",
                _ => "غير معروف"
            };
        }

        private string GetInvoiceStatusText(InvoiceStatus status)
        {
            return status switch
            {
                InvoiceStatus.Draft => "مسودة",
                InvoiceStatus.Sent => "مرسلة",
                InvoiceStatus.Paid => "مدفوعة",
                InvoiceStatus.PartiallyPaid => "مدفوعة جزئياً",
                InvoiceStatus.Overdue => "متأخرة",
                InvoiceStatus.Cancelled => "ملغية",
                _ => "غير معروف"
            };
        }

        private string GetPurchaseStatusText(PurchaseStatus status)
        {
            return status switch
            {
                PurchaseStatus.Pending => "معلق",
                PurchaseStatus.Ordered => "تم الطلب",
                PurchaseStatus.Received => "تم الاستلام",
                PurchaseStatus.PartiallyReceived => "استلام جزئي",
                PurchaseStatus.Cancelled => "ملغي",
                _ => "غير معروف"
            };
        }

        private void ApplyFilters()
        {
            // حماية من null للقائمة
            if (_allOrders == null)
                return;

            var filtered = _allOrders.AsEnumerable();

            // حماية من null للـ ComboBox النوع
            if (CmbTypeFilter != null && CmbTypeFilter.SelectedItem is ComboBoxItem typeItem && typeItem.Tag?.ToString() != "All")
            {
                var typeTag = typeItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(typeTag))
                    filtered = filtered.Where(o => o != null && o.OrderType == typeTag);
            }

            // حماية من null للـ ComboBox الحالة
            if (CmbStatusFilter != null && CmbStatusFilter.SelectedItem is ComboBoxItem statusItem && statusItem.Tag?.ToString() != "All")
            {
                var statusTag = statusItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(statusTag))
                    filtered = filtered.Where(o => o != null && o.Status == statusTag);
            }

            // حماية من null للـ ComboBox الحالة المالية
            if (CmbPaymentFilter != null && CmbPaymentFilter.SelectedItem is ComboBoxItem paymentItem && paymentItem.Tag?.ToString() != "All")
            {
                var paymentTag = paymentItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(paymentTag))
                {
                    filtered = paymentTag switch
                    {
                        "FullyPaid" => filtered.Where(o => o != null && o.RemainingAmount <= 0),
                        "PartiallyPaid" => filtered.Where(o => o != null && o.PaidAmount > 0 && o.RemainingAmount > 0),
                        "Unpaid" => filtered.Where(o => o != null && o.PaidAmount <= 0),
                        _ => filtered
                    };
                }
            }

            // فلتر التاريخ
            if (DpFromDate != null && DpFromDate.SelectedDate.HasValue)
            {
                var fromDate = DpFromDate.SelectedDate.Value.Date;
                filtered = filtered.Where(o => o != null && o.OrderDate.Date >= fromDate);
            }

            if (DpToDate != null && DpToDate.SelectedDate.HasValue)
            {
                var toDate = DpToDate.SelectedDate.Value.Date;
                filtered = filtered.Where(o => o != null && o.OrderDate.Date <= toDate);
            }

            // حماية من null للبحث
            if (TxtSearch != null && !string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                var search = TxtSearch.Text.ToLower();
                filtered = filtered.Where(o =>
                    o != null &&
                    ((o.OrderNumber ?? "").ToLower().Contains(search) ||
                    (o.PartyName ?? "").ToLower().Contains(search) ||
                    (o.Notes ?? "").ToLower().Contains(search))
                );
            }

            // حماية من null للـ DataGrid
            if (DgOrders != null)
            {
                var filteredList = filtered.ToList();
                DgOrders.ItemsSource = filteredList;

                // تحديث الإحصائيات للبيانات المفلترة
                UpdateFilteredStatistics(filteredList);
            }
        }

        private void UpdateFilteredStatistics(List<OrderViewModel> filteredOrders)
        {
            if (filteredOrders != null)
            {
                var totalOrders = filteredOrders.Count;
                var customerOrders = filteredOrders.Count(o => o.OrderType == "CustomerOrder");
                var invoices = filteredOrders.Count(o => o.OrderType == "Invoice");
                var purchases = filteredOrders.Count(o => o.OrderType == "Purchase");

                var totalPaid = filteredOrders.Sum(o => o.PaidAmount);
                var totalRemaining = filteredOrders.Sum(o => o.RemainingAmount);

                // تحديث نص الإحصائيات
                if (TxtStatistics != null)
                {
                    TxtStatistics.Text = $"المعروض: {totalOrders} | طلبات عملاء: {customerOrders} | فواتير: {invoices} | مشتريات: {purchases}";
                }

                // تحديث ملخص المدفوعات
                if (TxtPaymentSummary != null)
                {
                    TxtPaymentSummary.Text = $"مدفوع: {totalPaid:N0} ج.م | متبقي: {totalRemaining:N0} ج.م";
                }

                // تحديث عدد السجلات في الجدول
                if (TxtRecordCount != null)
                {
                    TxtRecordCount.Text = $"عدد السجلات: {totalOrders}";
                }
            }
        }

        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e) => ApplyFilters();
        private void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e) => ApplyFilters();
        private void CmbTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e) => ApplyFilters();
        private void CmbPaymentFilter_SelectionChanged(object sender, SelectionChangedEventArgs e) => ApplyFilters();
        private void DateFilter_Changed(object sender, SelectionChangedEventArgs e) => ApplyFilters();

        // الإجراءات
        private void BtnAddOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إظهار قائمة خيارات لنوع الطلب
                var result = MessageBox.Show("اختر نوع الطلب:\n\nنعم = طلب شراء من مورد\nلا = الذهاب لصفحة الفواتير",
                    "إضافة طلب جديد", MessageBoxButton.YesNoCancel, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إنشاء طلب شراء
                    try
                    {
                        var selectSupplierWindow = new SelectSupplierWindow();
                        if (selectSupplierWindow.ShowDialog() == true)
                        {
                            var selectedSupplier = selectSupplierWindow.SelectedSupplier;
                            if (selectedSupplier != null)
                            {
                                var addPurchaseWindow = new AddEditPurchaseOrderWindow(selectedSupplier);
                                if (addPurchaseWindow.ShowDialog() == true)
                                {
                                    LoadOrders();
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في إنشاء طلب الشراء:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else if (result == MessageBoxResult.No)
                {
                    // الذهاب لصفحة الفواتير
                    MessageBox.Show("سيتم توجيهك لصفحة الفواتير لإنشاء فاتورة مبيعات.",
                        "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ عام:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOrders.SelectedItem is OrderViewModel selectedOrder)
                {
                    // تحقق من نوع الطلب
                    if (selectedOrder.OrderType == "Info")
                    {
                        MessageBox.Show("لا يمكن تعديل هذا العنصر.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    if (selectedOrder.OrderType == "CustomerOrder")
                    {
                        try
                        {
                            using var context = new FactoryDbContext();
                            var customerOrder = context.CustomerOrders
                                .Include(o => o.Customer)
                                .Include(o => o.OrderItems)
                                .FirstOrDefault(o => o.OrderId == selectedOrder.OrderId);

                            if (customerOrder != null && customerOrder.Customer != null)
                            {
                                // استخدام النافذة الصحيحة للتعديل
                                var editWindow = new AddOrderWindow(customerOrder.Customer, customerOrder);
                                if (editWindow.ShowDialog() == true)
                                {
                                    LoadRealDataAsync();
                                    MessageBox.Show("تم تعديل الطلب بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                                }
                            }
                            else
                            {
                                MessageBox.Show("الطلب أو العميل غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else if (selectedOrder.OrderType == "Invoice")
                    {
                        MessageBox.Show("لتعديل الفواتير، يرجى الذهاب لصفحة الفواتير.",
                            "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else if (selectedOrder.OrderType == "Purchase")
                    {
                        try
                        {
                            using var context = new FactoryDbContext();
                            var purchase = context.Purchases
                                .Include(p => p.Supplier)
                                .FirstOrDefault(p => p.PurchaseId == selectedOrder.OrderId);

                            if (purchase != null && purchase.Supplier != null)
                            {
                                var editWindow = new AddEditPurchaseOrderWindow(purchase.Supplier, purchase);
                                if (editWindow.ShowDialog() == true)
                                {
                                    LoadOrders();
                                }
                            }
                            else
                            {
                                MessageBox.Show("لم يتم العثور على طلب الشراء أو المورد.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"خطأ في تحميل بيانات الطلب:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد طلب للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ عام في التعديل:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeleteOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOrders.SelectedItem is OrderViewModel selectedOrder)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الطلب رقم {selectedOrder.OrderNumber}؟\n\nتحذير: هذا الإجراء لا يمكن التراجع عنه!",
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        using var context = new FactoryDbContext();

                        // حذف الدفعات والحركات المالية المرتبطة
                        if (selectedOrder.OrderType == "CustomerOrder")
                        {
                            var customerOrder = context.CustomerOrders
                                .Include(o => o.OrderItems)
                                .Include(o => o.Customer)
                                .FirstOrDefault(o => o.OrderId == selectedOrder.OrderId);

                            if (customerOrder != null)
                            {
                                // حذف الدفعات المرتبطة
                                var payments = context.CustomerPayments.Where(p => p.OrderId == customerOrder.OrderId).ToList();
                                foreach (var payment in payments)
                                {
                                    // حذف الحركات المالية المرتبطة بالدفعة
                                    var cashTxs = context.CashTransactions.Where(t => t.Description.Contains($"طلب رقم: {customerOrder.OrderId}")).ToList();
                                    context.CashTransactions.RemoveRange(cashTxs);
                                }
                                context.CustomerPayments.RemoveRange(payments);

                                // تحديث رصيد العميل
                                if (customerOrder.Customer != null)
                                {
                                    var customer = customerOrder.Customer;
                                    customer.Balance -= customerOrder.TotalAmount;
                                }

                                // حذف عناصر الطلب أولاً
                                context.OrderItems.RemoveRange(customerOrder.OrderItems);
                                // ثم حذف الطلب
                                context.CustomerOrders.Remove(customerOrder);
                            }
                        }
                        else if (selectedOrder.OrderType == "Invoice")
                        {
                            var invoice = context.Invoices
                                .Include(i => i.Items)
                                .Include(i => i.Customer)
                                .FirstOrDefault(i => i.InvoiceId == selectedOrder.OrderId);

                            if (invoice != null)
                            {
                                // حذف عناصر الفاتورة أولاً
                                context.InvoiceItems.RemoveRange(invoice.Items);
                                // ثم حذف الفاتورة
                                context.Invoices.Remove(invoice);

                                // تحديث رصيد العميل
                                if (invoice.Customer != null)
                                {
                                    var customer = invoice.Customer;
                                    customer.Balance -= invoice.TotalAmount;
                                }
                            }
                        }
                        else if (selectedOrder.OrderType == "Purchase")
                        {
                            var purchase = context.Purchases
                                .Include(p => p.PurchaseItems)
                                .Include(p => p.Supplier)
                                .FirstOrDefault(p => p.PurchaseId == selectedOrder.OrderId);

                            if (purchase != null)
                            {
                                // حذف الدفعات المرتبطة
                                var payments = context.SupplierPayments.Where(p => p.PurchaseId == purchase.PurchaseId).ToList();
                                foreach (var payment in payments)
                                {
                                    var cashTxs = context.CashTransactions.Where(t => t.Description.Contains($"طلب رقم: {purchase.PurchaseId}")).ToList();
                                    context.CashTransactions.RemoveRange(cashTxs);
                                }
                                context.SupplierPayments.RemoveRange(payments);

                                // تحديث رصيد المورد
                                if (purchase.Supplier != null)
                                {
                                    var supplier = purchase.Supplier;
                                    supplier.Balance -= purchase.TotalAmount;
                                }

                                // حذف عناصر طلب الشراء أولاً
                                context.PurchaseItems.RemoveRange(purchase.PurchaseItems);
                                // ثم حذف طلب الشراء
                                context.Purchases.Remove(purchase);
                            }
                        }

                        context.SaveChanges();
                        LoadOrders();
                        MessageBox.Show("تم حذف الطلب وكل الدفعات والحركات المالية المرتبطة به بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد طلب للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ أثناء حذف الطلب:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnViewDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOrders.SelectedItem is OrderViewModel selectedOrder)
                {
                    if (selectedOrder.OrderType == "CustomerOrder")
                    {
                        using var context = new FactoryDbContext();
                        var customerOrder = context.CustomerOrders
                            .Include(o => o.Customer)
                            .Include(o => o.OrderItems)
                            .FirstOrDefault(o => o.OrderId == selectedOrder.OrderId);

                        if (customerOrder != null)
                        {
                            var details = $"تفاصيل طلب العميل رقم: {customerOrder.OrderNumber}\n\n";
                            details += $"العميل: {customerOrder.Customer?.Name ?? "غير محدد"}\n";
                            details += $"التاريخ: {customerOrder.OrderDate:yyyy/MM/dd}\n";
                            details += $"الحالة: {GetCustomerOrderStatusText(customerOrder.Status)}\n";
                            details += $"الإجمالي: {customerOrder.TotalAmount:N2} ج.م\n";
                            details += $"المدفوع: {customerOrder.PaidAmount:N2} ج.م\n";
                            details += $"المتبقي: {customerOrder.RemainingAmount:N2} ج.م\n";
                            details += $"الملاحظات: {customerOrder.Notes ?? "لا توجد"}\n\n";

                            if (customerOrder.OrderItems?.Any() == true)
                            {
                                details += "العناصر:\n";
                                foreach (var item in customerOrder.OrderItems)
                                {
                                    details += $"- {item.ProductName}: {item.Quantity} × {item.UnitPrice:N2} = {item.TotalPrice:N2} ج.م\n";
                                }
                            }

                            MessageBox.Show(details, "تفاصيل طلب العميل", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("طلب العميل غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else if (selectedOrder.OrderType == "Invoice")
                    {
                        using var context = new FactoryDbContext();
                        var invoice = context.Invoices
                            .Include(i => i.Customer)
                            .Include(i => i.Items)
                            .FirstOrDefault(i => i.InvoiceId == selectedOrder.OrderId);

                        if (invoice != null)
                        {
                            var previewWindow = new InvoicePreviewWindow(invoice);
                            previewWindow.ShowDialog();
                        }
                        else
                        {
                            MessageBox.Show("الفاتورة غير موجودة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else if (selectedOrder.OrderType == "Purchase")
                    {
                        using var context = new FactoryDbContext();
                        var purchase = context.Purchases
                            .Include(p => p.Supplier)
                            .Include(p => p.PurchaseItems)
                            .FirstOrDefault(p => p.PurchaseId == selectedOrder.OrderId);

                        if (purchase != null)
                        {
                            var detailsWindow = new PurchaseOrderDetailsWindow(purchase);
                            detailsWindow.ShowDialog();
                        }
                        else
                        {
                            MessageBox.Show("طلب الشراء غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد طلب لعرض التفاصيل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ أثناء عرض تفاصيل الطلب:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnViewInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOrders.SelectedItem is OrderViewModel selectedOrder)
                {
                    if (selectedOrder.OrderType == "Invoice")
                    {
                        // الطلب هو فاتورة بالفعل، عرضها
                        BtnViewDetails_Click(sender, e);
                    }
                    else if (selectedOrder.OrderType == "Purchase")
                    {
                        MessageBox.Show("لا يمكن إنشاء فاتورة لطلب الشراء. طلبات الشراء لا تحتاج فواتير.",
                            "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد طلب لعرض الفاتورة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ أثناء عرض الفاتورة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_allOrders == null || !_allOrders.Any())
                {
                    MessageBox.Show("لا توجد طلبات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var fileName = Helpers.ExcelExportHelper.ShowSaveDialog(
                    $"الطلبات_والفواتير_{DateTime.Now:yyyyMMdd_HHmmss}.csv",
                    "تصدير الطلبات والفواتير");

                if (!string.IsNullOrEmpty(fileName))
                {
                    var headers = new Dictionary<string, Func<OrderViewModel, object>>
                    {
                        { "الملاحظات", o => o.Notes ?? "-" },
                        { "المتبقي", o => $"{o.RemainingAmount:N2} ج.م" },
                        { "المدفوع", o => $"{o.PaidAmount:N2} ج.م" },
                        { "الإجمالي", o => $"{o.TotalAmount:N2} ج.م" },
                        { "الحالة", o => o.StatusText ?? "-" },
                        { "التاريخ", o => o.OrderDate.ToString("yyyy/MM/dd") },
                        { "العميل/المورد", o => o.PartyName ?? "-" },
                        { "النوع", o => o.OrderTypeText ?? "-" },
                        { "رقم الطلب", o => o.OrderNumber ?? "-" }
                    };

                    var summary = new Dictionary<string, decimal>
                    {
                        { "إجمالي الطلبات", _allOrders.Count() },
                        { "إجمالي المبالغ", _allOrders.Sum(o => o.TotalAmount) },
                        { "إجمالي المدفوع", _allOrders.Sum(o => o.PaidAmount) },
                        { "إجمالي المتبقي", _allOrders.Sum(o => o.RemainingAmount) }
                    };

                    Helpers.ExcelExportHelper.ExportFinancialReport(
                        _allOrders,
                        headers,
                        fileName,
                        "الطلبات والفواتير",
                        summary);

                    MessageBox.Show($"تم تصدير {_allOrders.Count()} طلب بنجاح إلى:\n{fileName}",
                        "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ أثناء تصدير الطلبات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // TODO: طباعة الطلبات
                MessageBox.Show("سيتم إضافة ميزة الطباعة قريباً", "الطباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ أثناء طباعة الطلبات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // الدوال الجديدة للواجهة المحسنة
        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadRealDataAsync();
            if (TxtLastUpdate != null)
                TxtLastUpdate.Text = EgyptTimeHelper.Now.ToString("HH:mm:ss");
            MessageBox.Show("تم تحديث البيانات بنجاح!", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnClearFilters_Click(object sender, RoutedEventArgs e)
        {
            // مسح جميع الفلاتر
            if (TxtSearch != null) TxtSearch.Text = "";
            if (CmbTypeFilter != null) CmbTypeFilter.SelectedIndex = 0;
            if (CmbStatusFilter != null) CmbStatusFilter.SelectedIndex = 0;
            if (CmbPaymentFilter != null) CmbPaymentFilter.SelectedIndex = 0;
            if (DpFromDate != null) DpFromDate.SelectedDate = null;
            if (DpToDate != null) DpToDate.SelectedDate = null;

            ApplyFilters();
            MessageBox.Show("تم مسح جميع الفلاتر!", "مسح الفلاتر", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void BtnViewOptions_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("خيارات العرض:\n- تغيير حجم الأعمدة\n- إخفاء/إظهار الأعمدة\n- ترتيب البيانات\n\nسيتم إضافة هذه الميزات قريباً!",
                "خيارات العرض", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // أزرار الإضافة الجديدة
        private void BtnAddCustomerOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // اختيار العميل أولاً
                var selectCustomerWindow = new SelectCustomerWindow();
                if (selectCustomerWindow.ShowDialog() == true && selectCustomerWindow.SelectedCustomer != null)
                {
                    var addOrderWindow = new AddOrderWindow(selectCustomerWindow.SelectedCustomer);
                    if (addOrderWindow.ShowDialog() == true)
                    {
                        // تحديث البيانات بعد إضافة طلب جديد
                        LoadRealDataAsync();
                        MessageBox.Show("تم إضافة الطلب بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة الطلب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddPurchaseOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة اختيار المورد أولاً
                var selectSupplierWindow = new SelectSupplierWindow();
                if (selectSupplierWindow.ShowDialog() == true)
                {
                    var selectedSupplier = selectSupplierWindow.SelectedSupplier;
                    if (selectedSupplier != null)
                    {
                        // فتح نافذة إضافة طلب شراء جديد
                        var addPurchaseWindow = new AddEditPurchaseOrderWindow(selectedSupplier);
                        if (addPurchaseWindow.ShowDialog() == true)
                        {
                            LoadRealDataAsync();
                            MessageBox.Show("تم إضافة طلب الشراء بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                    else
                    {
                        MessageBox.Show("يرجى اختيار مورد صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة طلب الشراء: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddInvoice_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // فتح نافذة اختيار العميل أولاً
                var selectCustomerWindow = new SelectCustomerWindow();
                if (selectCustomerWindow.ShowDialog() == true)
                {
                    var selectedCustomer = selectCustomerWindow.SelectedCustomer;
                    if (selectedCustomer != null)
                    {
                        // فتح نافذة اختيار الطلبات غير المفوترة لهذا العميل
                        var selectOrdersWindow = new SelectOrdersForInvoiceWindow(selectedCustomer);
                        if (selectOrdersWindow.ShowDialog() == true)
                        {
                            // تم إنشاء الفاتورة بنجاح في النافذة
                            LoadRealDataAsync();
                            MessageBox.Show("تم إنشاء الفاتورة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                    else
                    {
                        MessageBox.Show("يرجى اختيار عميل صحيح", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // أزرار التقارير والتصدير
        private void BtnDetailedReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء تقرير مفصل
                var ordersToReport = DgOrders.ItemsSource as List<OrderViewModel>;
                if (ordersToReport == null || !ordersToReport.Any())
                {
                    MessageBox.Show("لا توجد بيانات لإنشاء التقرير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء نافذة التقرير المفصل
                var reportWindow = new OrdersReportWindow(ordersToReport);
                reportWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير المفصل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        // أزرار الإجراءات الجديدة في الجدول
        private void BtnAddPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgOrders.SelectedItem is OrderViewModel selectedOrder)
                {
                    if (selectedOrder.RemainingAmount <= 0)
                    {
                        MessageBox.Show("هذا الطلب مدفوع بالكامل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    // التحقق من نوع الطلب وفتح النافذة المناسبة
                    if (selectedOrder.OrderType == "CustomerOrder" && selectedOrder.CustomerId.HasValue)
                    {
                        // فتح نافذة دفع العميل
                        using var context = new FactoryDbContext();
                        var customer = context.Customers.Find(selectedOrder.CustomerId.Value);

                        if (customer != null)
                        {
                            var addPaymentWindow = new AddCustomerPaymentWindow(customer);
                            if (addPaymentWindow.ShowDialog() == true)
                            {
                                LoadRealDataAsync();
                                MessageBox.Show("تم إضافة الدفعة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                        else
                        {
                            MessageBox.Show("العميل غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else if (selectedOrder.OrderType == "Purchase" && selectedOrder.SupplierId.HasValue)
                    {
                        // فتح نافذة دفع المورد
                        using var context = new FactoryDbContext();
                        var supplier = context.Suppliers.Find(selectedOrder.SupplierId.Value);

                        if (supplier != null)
                        {
                            var addPaymentWindow = new AddSupplierPaymentWindow(supplier);
                            if (addPaymentWindow.ShowDialog() == true)
                            {
                                LoadRealDataAsync();
                                MessageBox.Show("تم إضافة الدفعة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                        else
                        {
                            MessageBox.Show("المورد غير موجود", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("نوع الطلب غير مدعوم للدفع", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى تحديد طلب لإضافة دفعة له", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة الدفعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        // دالة تحديث معلومات التحديد
        private void DgOrders_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (TxtSelectionInfo != null)
            {
                if (DgOrders.SelectedItem is OrderViewModel selectedOrder)
                {
                    TxtSelectionInfo.Text = $"محدد: {selectedOrder.OrderNumber} - {selectedOrder.PartyName} - {selectedOrder.TotalAmount:N0} ج.م";
                }
                else
                {
                    TxtSelectionInfo.Text = "لم يتم تحديد أي عنصر";
                }
            }

            // تحديث عدد السجلات المعروضة
            if (TxtRecordCount != null && DgOrders.ItemsSource != null)
            {
                var count = ((System.Collections.IEnumerable)DgOrders.ItemsSource).Cast<object>().Count();
                TxtRecordCount.Text = $"عدد السجلات: {count}";
            }
        }
    }

    public class OrderViewModel
    {
        public int OrderId { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public string OrderType { get; set; } = string.Empty; // Customer/Purchase
        public string OrderTypeText { get; set; } = string.Empty;
        public string PartyName { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string StatusText { get; set; } = string.Empty;
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string Notes { get; set; } = string.Empty;

        // إضافة معرفات الأطراف المرتبطة
        public int? CustomerId { get; set; }
        public int? SupplierId { get; set; }
    }
} 