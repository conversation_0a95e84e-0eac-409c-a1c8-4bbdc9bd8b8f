using System.Windows;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;

namespace FactoryManagementSystem.Views;

/// <summary>
/// نافذة إضافة/تعديل العامل
/// </summary>
public partial class AddEditEmployeeWindow : Window
{
    private readonly FactoryDbContext _context;
    private readonly Employee? _employee;
    private readonly bool _isEditMode;

    public AddEditEmployeeWindow(Employee? employee = null)
    {
        InitializeComponent();
        _context = new FactoryDbContext();
        _employee = employee;
        _isEditMode = employee != null;

        InitializeDefaults();

        if (_isEditMode)
        {
            TxtTitle.Text = "تعديل بيانات العامل";
            LoadEmployeeData();
        }
        else
        {
            TxtTitle.Text = "إضافة عامل جديد";
        }
    }

    private async void InitializeDefaults()
    {
        DpHireDate.SelectedDate = DateTime.Today;
        DpBirthDate.SelectedDate = DateTime.Today.AddYears(-25);

        // تعيين القسم الافتراضي إلى "الإنتاج"
        CmbDepartment.SelectedIndex = 0; // الإنتاج

        // إنشاء رقم عامل تلقائي للعمال الجدد فقط
        if (!_isEditMode)
        {
            TxtEmployeeNumber.Text = await GenerateEmployeeNumber();
        }
    }

    /// <summary>
    /// إنشاء رقم عامل تلقائي بتنسيق EMP001, EMP002, إلخ
    /// </summary>
    private async Task<string> GenerateEmployeeNumber()
    {
        try
        {
            // البحث عن آخر رقم عامل في قاعدة البيانات
            var lastEmployee = await _context.Employees
                .Where(e => e.EmployeeNumber.StartsWith("EMP"))
                .OrderByDescending(e => e.EmployeeNumber)
                .FirstOrDefaultAsync();

            if (lastEmployee == null)
            {
                return "EMP001"; // أول عامل
            }

            // استخراج الرقم من آخر رقم عامل
            var lastNumberStr = lastEmployee.EmployeeNumber.Substring(3); // إزالة "EMP"
            if (int.TryParse(lastNumberStr, out var lastNumber))
            {
                var nextNumber = lastNumber + 1;
                return $"EMP{nextNumber:D3}"; // تنسيق بثلاثة أرقام (001, 002, إلخ)
            }

            // في حالة فشل التحليل، إنشاء رقم جديد بناءً على العدد الإجمالي
            var totalEmployees = await _context.Employees.CountAsync();
            return $"EMP{(totalEmployees + 1):D3}";
        }
        catch (Exception)
        {
            // في حالة حدوث خطأ، إنشاء رقم عشوائي
            var random = new Random();
            return $"EMP{random.Next(1, 9999):D3}";
        }
    }

    private void LoadEmployeeData()
    {
        if (_employee == null) return;

        TxtName.Text = _employee.Name;
        TxtEmployeeNumber.Text = _employee.EmployeeNumber;
        TxtNationalId.Text = _employee.NationalId;
        TxtPhone.Text = _employee.Phone;
        DpBirthDate.SelectedDate = _employee.BirthDate;
        TxtAddress.Text = _employee.Address;
        DpHireDate.SelectedDate = _employee.HireDate;
        TxtPosition.Text = _employee.Position;
        CmbDepartment.Text = _employee.Department;
        TxtBasicSalary.Text = _employee.BasicSalary.ToString("F2");
        TxtHourlyRate.Text = _employee.HourlyRate.ToString("F2");
        TxtWorkingHoursPerDay.Text = _employee.WorkingHoursPerDay.ToString();
        TxtWorkingDaysPerWeek.Text = _employee.WorkingDaysPerWeek.ToString();
        ChkIsActive.IsChecked = _employee.IsActive;
        TxtNotes.Text = _employee.Notes;
    }

    private bool ValidateInput()
    {
        if (string.IsNullOrWhiteSpace(TxtName.Text))
        {
            MessageBox.Show("يرجى إدخال اسم العامل", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtName.Focus();
            return false;
        }

        if (string.IsNullOrWhiteSpace(TxtEmployeeNumber.Text))
        {
            MessageBox.Show("يرجى إدخال رقم العامل", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtEmployeeNumber.Focus();
            return false;
        }

        // المنصب أصبح اختياري - لا حاجة للتحقق منه

        if (string.IsNullOrWhiteSpace(CmbDepartment.Text))
        {
            MessageBox.Show("يرجى إدخال القسم", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            CmbDepartment.Focus();
            return false;
        }

        if (!decimal.TryParse(TxtBasicSalary.Text, out _))
        {
            MessageBox.Show("يرجى إدخال راتب أساسي صحيح", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtBasicSalary.Focus();
            return false;
        }

        if (!decimal.TryParse(TxtHourlyRate.Text, out _))
        {
            MessageBox.Show("يرجى إدخال أجر ساعة صحيح", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtHourlyRate.Focus();
            return false;
        }

        if (!int.TryParse(TxtWorkingHoursPerDay.Text, out var hoursPerDay) || hoursPerDay <= 0)
        {
            MessageBox.Show("يرجى إدخال عدد ساعات عمل يومية صحيح", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtWorkingHoursPerDay.Focus();
            return false;
        }

        if (!int.TryParse(TxtWorkingDaysPerWeek.Text, out var daysPerWeek) || daysPerWeek <= 0 || daysPerWeek > 7)
        {
            MessageBox.Show("يرجى إدخال عدد أيام عمل أسبوعية صحيح (1-7)", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            TxtWorkingDaysPerWeek.Focus();
            return false;
        }

        if (!DpHireDate.SelectedDate.HasValue)
        {
            MessageBox.Show("يرجى إدخال تاريخ التوظيف", "خطأ في البيانات", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            DpHireDate.Focus();
            return false;
        }

        return true;
    }

    private async void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        if (!ValidateInput()) return;

        try
        {
            Employee employee;

            if (_isEditMode && _employee != null)
            {
                // تعديل عامل موجود
                employee = _employee;
                employee.LastModifiedDate = DateTime.Now;
            }
            else
            {
                // إضافة عامل جديد
                employee = new Employee();
                employee.CreatedDate = DateTime.Now;
                
                // التحقق من عدم تكرار رقم العامل
                var existingEmployee = await _context.Employees
                    .FirstOrDefaultAsync(e => e.EmployeeNumber == TxtEmployeeNumber.Text.Trim());
                if (existingEmployee != null)
                {
                    MessageBox.Show("رقم العامل موجود مسبقاً، يرجى استخدام رقم آخر", "خطأ في البيانات", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtEmployeeNumber.Focus();
                    return;
                }
                
                _context.Employees.Add(employee);
            }

            // تحديث البيانات
            employee.Name = TxtName.Text.Trim();
            employee.EmployeeNumber = TxtEmployeeNumber.Text.Trim();
            employee.NationalId = string.IsNullOrWhiteSpace(TxtNationalId.Text) ? null : TxtNationalId.Text.Trim();
            employee.Phone = string.IsNullOrWhiteSpace(TxtPhone.Text) ? null : TxtPhone.Text.Trim();
            employee.BirthDate = DpBirthDate.SelectedDate;
            employee.Address = string.IsNullOrWhiteSpace(TxtAddress.Text) ? null : TxtAddress.Text.Trim();
            employee.HireDate = DpHireDate.SelectedDate!.Value;
            employee.Position = string.IsNullOrWhiteSpace(TxtPosition.Text) ? "غير محدد" : TxtPosition.Text.Trim();
            employee.Department = CmbDepartment.Text.Trim();
            employee.BasicSalary = decimal.Parse(TxtBasicSalary.Text);
            employee.HourlyRate = decimal.Parse(TxtHourlyRate.Text);
            employee.WorkingHoursPerDay = int.Parse(TxtWorkingHoursPerDay.Text);
            employee.WorkingDaysPerWeek = int.Parse(TxtWorkingDaysPerWeek.Text);
            employee.IsActive = ChkIsActive.IsChecked ?? true;
            employee.Notes = string.IsNullOrWhiteSpace(TxtNotes.Text) ? null : TxtNotes.Text.Trim();

            await _context.SaveChangesAsync();

            MessageBox.Show(
                _isEditMode ? "تم تحديث بيانات العامل بنجاح" : "تم إضافة العامل بنجاح", 
                "نجح", 
                MessageBoxButton.OK, 
                MessageBoxImage.Information);

            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        _context?.Dispose();
        base.OnClosed(e);
    }
}
