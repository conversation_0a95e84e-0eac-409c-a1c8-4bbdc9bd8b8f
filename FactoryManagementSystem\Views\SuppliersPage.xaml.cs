using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;

namespace FactoryManagementSystem.Views;

/// <summary>
/// صفحة إدارة الموردين
/// </summary>
public partial class SuppliersPage : Page
{
    private readonly FactoryDbContext _context = new();
    public ObservableCollection<Supplier> Suppliers { get; set; } = new();
    private Supplier? _selectedSupplier;

    public SuppliersPage()
    {
        InitializeComponent();
        DgSuppliers.ItemsSource = Suppliers;

        LoadSuppliers();
    }

    private async void LoadSuppliers()
    {
        try
        {
            Suppliers.Clear();
            var suppliers = await _context.Suppliers
                .OrderBy(s => s.Name)
                .ToListAsync();

            foreach (var supplier in suppliers)
            {
                Suppliers.Add(supplier);
            }

            UpdateStatistics();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الموردين: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics()
    {
        TxtTotalSuppliers.Text = Suppliers.Count.ToString();
        TxtActiveSuppliers.Text = Suppliers.Count(s => s.IsActive).ToString();
    }

    private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        try
        {
            var searchText = TxtSearch?.Text?.ToLower() ?? "";
            var statusFilter = CmbStatusFilter?.SelectedIndex ?? 0;

            var filteredSuppliers = Suppliers.AsEnumerable();

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                filteredSuppliers = filteredSuppliers.Where(s =>
                    s.Name.ToLower().Contains(searchText) ||
                    (s.CompanyName != null && s.CompanyName.ToLower().Contains(searchText)) ||
                    (s.Phone != null && s.Phone.Contains(searchText)) ||
                    (s.City != null && s.City.ToLower().Contains(searchText))
                );
            }

            // تطبيق فلتر الحالة
            switch (statusFilter)
            {
                case 1: // نشط
                    filteredSuppliers = filteredSuppliers.Where(s => s.IsActive);
                    break;
                case 2: // غير نشط
                    filteredSuppliers = filteredSuppliers.Where(s => !s.IsActive);
                    break;
                default: // الكل
                    break;
            }

            DgSuppliers.ItemsSource = filteredSuppliers.ToList();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DgSuppliers_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        _selectedSupplier = DgSuppliers.SelectedItem as Supplier;
        BtnEditSupplier.IsEnabled = _selectedSupplier != null;
        BtnDeleteSupplier.IsEnabled = _selectedSupplier != null;
        BtnSupplierOrders.IsEnabled = _selectedSupplier != null;
        BtnSupplierPayments.IsEnabled = _selectedSupplier != null;
        BtnSupplierStatement.IsEnabled = _selectedSupplier != null;

    }

    private void DgSuppliers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (_selectedSupplier != null)
        {
            EditSupplier(_selectedSupplier);
        }
    }

    private void BtnAddSupplier_Click(object sender, RoutedEventArgs e)
    {
        var addWindow = new AddEditSupplierWindow();
        if (addWindow.ShowDialog() == true)
        {
            LoadSuppliers();
        }
    }

    private void BtnEditSupplier_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedSupplier != null)
        {
            EditSupplier(_selectedSupplier);
        }
    }

    private void EditSupplier(Supplier supplier)
    {
        var editWindow = new AddEditSupplierWindow(supplier);
        if (editWindow.ShowDialog() == true)
        {
            LoadSuppliers();
        }
    }

    private async void BtnDeleteSupplier_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedSupplier == null) return;

        var result = MessageBox.Show(
            $"هل أنت متأكد من حذف المورد '{_selectedSupplier.Name}'؟\n\nملاحظة: سيتم إلغاء تفعيل المورد بدلاً من حذفه نهائياً.",
            "تأكيد الحذف",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                _selectedSupplier.IsActive = false;
                _selectedSupplier.LastModifiedDate = DateTime.Now;

                await _context.SaveChangesAsync();
                LoadSuppliers();

                MessageBox.Show("تم إلغاء تفعيل المورد بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnSupplierOrders_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedSupplier == null)
        {
            MessageBox.Show("يرجى اختيار مورد أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            var ordersWindow = new SupplierOrdersWindow(_selectedSupplier);
            ordersWindow.ShowDialog();

            // تحديث البيانات بعد إغلاق النافذة
            LoadSuppliers();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح نافذة طلبات الشراء: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSupplierPayments_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedSupplier != null)
        {
            try
            {
                var paymentsWindow = new SupplierPaymentsWindow(_selectedSupplier);
                paymentsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة مدفوعات المورد: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnSupplierStatement_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedSupplier != null)
        {
            try
            {
                var statementWindow = new SupplierStatementWindow(_selectedSupplier);
                statementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح كشف حساب المورد: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }



    private void BtnAddSampleData_Click(object sender, RoutedEventArgs e)
    {
        AddSampleData();
    }

    private async void AddSampleData()
    {
        try
        {
            // التحقق من وجود بيانات
            var existingCount = await _context.Suppliers.CountAsync();
            if (existingCount > 0)
            {
                MessageBox.Show("توجد بيانات موردين بالفعل!", "معلومات",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var sampleSuppliers = new List<Supplier>
            {
                new Supplier
                {
                    Name = "شركة النسيج المصري",
                    CompanyName = "Egyptian Textile Co.",
                    Phone = "01012345678",
                    Address = "شارع الصناعة، المحلة الكبرى",
                    City = "المحلة الكبرى",
                    Email = "<EMAIL>",
                    Balance = 15000,
                    CreditLimit = 50000,
                    IsActive = true,
                    Notes = "مورد رئيسي للقطن والخيوط",
                    CreatedDate = EgyptTimeHelper.Now
                },
                new Supplier
                {
                    Name = "مصنع الألوان الحديث",
                    CompanyName = "Modern Colors Factory",
                    Phone = "01098765432",
                    Address = "المنطقة الصناعية، 6 أكتوبر",
                    City = "6 أكتوبر",
                    Email = "<EMAIL>",
                    Balance = 8500,
                    CreditLimit = 30000,
                    IsActive = true,
                    Notes = "متخصص في الأصباغ والكيماويات",
                    CreatedDate = EgyptTimeHelper.Now
                },
                new Supplier
                {
                    Name = "شركة الخيوط الذهبية",
                    CompanyName = "Golden Threads Ltd.",
                    Phone = "01155443322",
                    Address = "شارع النيل، دمياط",
                    City = "دمياط",
                    Balance = 12000,
                    CreditLimit = 40000,
                    IsActive = true,
                    Notes = "مورد خيوط عالية الجودة",
                    CreatedDate = EgyptTimeHelper.Now
                }
            };

            _context.Suppliers.AddRange(sampleSuppliers);
            await _context.SaveChangesAsync();

            // إعادة تحميل البيانات
            Suppliers.Clear();
            var suppliers = await _context.Suppliers.OrderBy(s => s.Name).ToListAsync();
            foreach (var supplier in suppliers)
            {
                Suppliers.Add(supplier);
            }
            UpdateStatistics();

            MessageBox.Show("تم إضافة بيانات تجريبية للموردين!", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة البيانات التجريبية: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تحديث بيانات الموردين
    /// </summary>
    private async void BtnRefreshSuppliers_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // تعطيل الزر أثناء التحديث
            BtnRefreshSuppliers.IsEnabled = false;
            BtnRefreshSuppliers.Content = "🔄 جاري التحديث...";

            // إعادة تحميل البيانات
            await Task.Run(() =>
            {
                Dispatcher.Invoke(() =>
                {
                    LoadSuppliers();
                });
            });

            // رسالة نجاح
            MessageBox.Show("تم تحديث بيانات الموردين بنجاح! ✅",
                "تحديث البيانات",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}",
                "خطأ",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }
        finally
        {
            // إعادة تفعيل الزر
            BtnRefreshSuppliers.IsEnabled = true;
            BtnRefreshSuppliers.Content = "🔄 تحديث";
        }
    }

    ~SuppliersPage()
    {
        _context?.Dispose();
    }
}
