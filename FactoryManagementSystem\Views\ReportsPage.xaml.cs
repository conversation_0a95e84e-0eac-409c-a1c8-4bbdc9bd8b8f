using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.IO;
using System.Diagnostics;

namespace FactoryManagementSystem.Views;

/// <summary>
/// أنواع فترات التقرير
/// </summary>
public enum ReportPeriodType
{
    Daily,
    Weekly,
    Monthly,
    Yearly
}

/// <summary>
/// صفحة التقارير والإحصائيات المرئية
/// </summary>
public partial class ReportsPage : Page
{
    private readonly FactoryDbContext? _context;
    private ReportPeriodType _currentPeriod = ReportPeriodType.Daily;

    public ReportsPage()
    {
        try
        {
            InitializeComponent();
            _context = new FactoryDbContext();

            // تحميل البيانات عند فتح الصفحة
            LoadReportsData();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل صفحة التقارير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);

            // تعيين قيم افتراضية في حالة الخطأ
            SetDefaultValues();
        }
    }

    /// <summary>
    /// تعيين القيم الافتراضية في حالة الخطأ
    /// </summary>
    private void SetDefaultValues()
    {
        try
        {
            if (TxtTotalOrders != null) TxtTotalOrders.Text = "0";
            if (TxtTotalInvoices != null) TxtTotalInvoices.Text = "0";
            if (TxtTotalCustomers != null) TxtTotalCustomers.Text = "0";
            if (TxtTotalPayments != null) TxtTotalPayments.Text = "0 ج.م";
            if (TxtLastOrderDate != null) TxtLastOrderDate.Text = "غير متوفر";
            if (TxtLastPaymentDate != null) TxtLastPaymentDate.Text = "غير متوفر";
            if (TxtAverageOrderValue != null) TxtAverageOrderValue.Text = "0 ج.م";

            // القيم المالية
            if (TxtTotalRevenue != null) TxtTotalRevenue.Text = "0 ج.م";
            if (TxtTotalExpenses != null) TxtTotalExpenses.Text = "0 ج.م";
            if (TxtNetProfit != null) TxtNetProfit.Text = "0 ج.م";
            if (TxtProfitArrow != null) TxtProfitArrow.Text = "🔺";
        }
        catch
        {
            // تجاهل أي أخطاء في تعيين القيم الافتراضية
        }
    }

    /// <summary>
    /// معالجة تغيير فترة التقرير
    /// </summary>
    private void ReportPeriod_Changed(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is RadioButton rb)
            {
                _currentPeriod = rb.Name switch
                {
                    "RbDaily" => ReportPeriodType.Daily,
                    "RbWeekly" => ReportPeriodType.Weekly,
                    "RbMonthly" => ReportPeriodType.Monthly,
                    "RbYearly" => ReportPeriodType.Yearly,
                    _ => ReportPeriodType.Daily
                };

                UpdatePeriodInfo();
                LoadReportsData(); // إعادة تحميل البيانات حسب الفترة الجديدة
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تغيير فترة التقرير: {ex.Message}");
        }
    }

    /// <summary>
    /// تحديث معلومات الفترة المحددة
    /// </summary>
    private void UpdatePeriodInfo()
    {
        try
        {
            if (TxtSelectedPeriod == null) return;

            var now = DateTime.Now;
            string periodText = _currentPeriod switch
            {
                ReportPeriodType.Daily => $"الفترة المحددة: {now:dd/MM/yyyy} (اليوم الحالي)",
                ReportPeriodType.Weekly => $"الفترة المحددة: من {now.AddDays(-(int)now.DayOfWeek):dd/MM/yyyy} إلى {now.AddDays(6-(int)now.DayOfWeek):dd/MM/yyyy} (الأسبوع الحالي)",
                ReportPeriodType.Monthly => $"الفترة المحددة: {now:MM/yyyy} (الشهر الحالي)",
                ReportPeriodType.Yearly => $"الفترة المحددة: {now:yyyy} (السنة الحالية)",
                _ => "الفترة المحددة: اليوم الحالي"
            };

            TxtSelectedPeriod.Text = periodText;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث معلومات الفترة: {ex.Message}");
        }
    }

    /// <summary>
    /// تحميل جميع بيانات التقارير
    /// </summary>
    private async void LoadReportsData()
    {
        try
        {
            // تعيين قيم افتراضية أولاً
            SetDefaultValues();

            // محاولة تحميل البيانات الفعلية
            if (_context != null)
            {
                await LoadStatisticsCards();
                await LoadFinancialSummary();
                await LoadQuickSummary();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل بيانات التقارير: {ex.Message}");

            // في حالة الخطأ، تأكد من وجود القيم الافتراضية
            SetDefaultValues();

            // عرض رسالة خطأ بسيطة
            MessageBox.Show("تعذر تحميل بعض بيانات التقارير. سيتم عرض قيم افتراضية.", "تنبيه",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    /// <summary>
    /// تحميل بيانات البطاقات الإحصائية
    /// </summary>
    private async System.Threading.Tasks.Task LoadStatisticsCards()
    {
        try
        {
            if (_context?.CustomerOrders == null) return;

            var (startDate, endDate) = GetPeriodDates();

            // إجمالي الطلبات في الفترة المحددة
            var totalOrders = await _context.CustomerOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .CountAsync();
            if (TxtTotalOrders != null)
                TxtTotalOrders.Text = totalOrders.ToString("N0");

            // عدد الفواتير في الفترة المحددة
            var totalInvoices = await _context.Invoices
                .Where(i => i.InvoiceDate >= startDate && i.InvoiceDate <= endDate)
                .CountAsync();
            if (TxtTotalInvoices != null)
                TxtTotalInvoices.Text = totalInvoices.ToString("N0");

            // عدد العملاء النشطين (لا يتأثر بالفترة)
            var totalCustomers = await _context.Customers.CountAsync(c => c.IsActive);
            if (TxtTotalCustomers != null)
                TxtTotalCustomers.Text = totalCustomers.ToString("N0");

            // إجمالي المدفوعات في الفترة المحددة
            var totalPayments = await _context.CustomerPayments
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .SumAsync(p => p.Amount);
            if (TxtTotalPayments != null)
                TxtTotalPayments.Text = $"{totalPayments:N0} ج.م";
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}");

            // في حالة الخطأ، عرض قيم افتراضية
            if (TxtTotalOrders != null) TxtTotalOrders.Text = "0";
            if (TxtTotalInvoices != null) TxtTotalInvoices.Text = "0";
            if (TxtTotalCustomers != null) TxtTotalCustomers.Text = "0";
            if (TxtTotalPayments != null) TxtTotalPayments.Text = "0 ج.م";
        }
    }

    /// <summary>
    /// الحصول على تواريخ بداية ونهاية الفترة المحددة
    /// </summary>
    private (DateTime startDate, DateTime endDate) GetPeriodDates()
    {
        var now = DateTime.Now;

        switch (_currentPeriod)
        {
            case ReportPeriodType.Daily:
                return (now.Date, now.Date.AddDays(1).AddTicks(-1));

            case ReportPeriodType.Weekly:
                var startOfWeek = now.AddDays(-(int)now.DayOfWeek);
                return (startOfWeek.Date, startOfWeek.Date.AddDays(7).AddTicks(-1));

            case ReportPeriodType.Monthly:
                var startOfMonth = new DateTime(now.Year, now.Month, 1);
                return (startOfMonth, startOfMonth.AddMonths(1).AddTicks(-1));

            case ReportPeriodType.Yearly:
                var startOfYear = new DateTime(now.Year, 1, 1);
                return (startOfYear, startOfYear.AddYears(1).AddTicks(-1));

            default:
                return (now.Date, now.Date.AddDays(1).AddTicks(-1));
        }
    }

    /// <summary>
    /// تحميل الملخص المالي الشامل
    /// </summary>
    private async System.Threading.Tasks.Task LoadFinancialSummary()
    {
        try
        {
            if (_context?.CashTransactions == null) return;

            var (startDate, endDate) = GetPeriodDates();

            // ✅ الطريقة الصحيحة: استخدام TransactionType كمعيار أساسي
            // حساب إجمالي الإيرادات من حركات الخزينة (حسب النوع: Income)
            var cashRevenue = await _context.CashTransactions
                .Where(t => t.TransactionDate >= startDate &&
                           t.TransactionDate <= endDate &&
                           t.Type == Models.TransactionType.Income)  // ✅ جميع عمليات القبض
                .SumAsync(t => t.Amount);

            // حساب إجمالي المصروفات من حركات الخزينة (حسب النوع: Expense)
            var cashExpenses = await _context.CashTransactions
                .Where(t => t.TransactionDate >= startDate &&
                           t.TransactionDate <= endDate &&
                           t.Type == Models.TransactionType.Expense)  // ✅ جميع عمليات الصرف
                .SumAsync(t => t.Amount);

            // إضافة مبيعات من الطلبات (إيرادات إضافية)
            var ordersRevenue = await _context.CustomerOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .SumAsync(o => o.TotalAmount);

            // إضافة فواتير المبيعات (إيرادات إضافية)
            var salesInvoicesRevenue = await _context.Invoices
                .Where(i => i.InvoiceDate >= startDate &&
                           i.InvoiceDate <= endDate &&
                           i.Type == InvoiceType.Sales)
                .SumAsync(i => i.TotalAmount);

            // إضافة فواتير المشتريات (مصروفات إضافية)
            var purchaseInvoicesExpenses = await _context.Invoices
                .Where(i => i.InvoiceDate >= startDate &&
                           i.InvoiceDate <= endDate &&
                           i.Type == InvoiceType.Purchase)
                .SumAsync(i => i.TotalAmount);

            // الإجمالي النهائي
            var finalRevenue = cashRevenue + ordersRevenue + salesInvoicesRevenue;
            var finalExpenses = cashExpenses + purchaseInvoicesExpenses;

            // حساب صافي الربح/الخسارة
            var netProfit = finalRevenue - finalExpenses;

            // طباعة تفاصيل للتشخيص
            System.Diagnostics.Debug.WriteLine($"=== تفاصيل التقرير المالي (الواجهة) - مُصحح ===");
            System.Diagnostics.Debug.WriteLine($"الفترة: {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
            System.Diagnostics.Debug.WriteLine($"✅ إيرادات الخزينة (Type=Income): {cashRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إيرادات الطلبات: {ordersRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إيرادات فواتير المبيعات: {salesInvoicesRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إجمالي الإيرادات: {finalRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"✅ مصروفات الخزينة (Type=Expense): {cashExpenses:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"مصروفات فواتير المشتريات: {purchaseInvoicesExpenses:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إجمالي المصروفات: {finalExpenses:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"صافي الربح/الخسارة: {netProfit:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"=== نهاية تفاصيل الواجهة المُصححة ===");

            // تحديث واجهة المستخدم
            UpdateFinancialUI(finalRevenue, finalExpenses, netProfit);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الملخص المالي: {ex.Message}");

            // في حالة الخطأ، عرض قيم افتراضية
            UpdateFinancialUI(0, 0, 0);
        }
    }

    /// <summary>
    /// تحديث واجهة المستخدم للملخص المالي
    /// </summary>
    private void UpdateFinancialUI(decimal totalRevenue, decimal totalExpenses, decimal netProfit)
    {
        try
        {
            // تحديث النصوص
            if (TxtTotalRevenue != null)
                TxtTotalRevenue.Text = $"{totalRevenue:N0} ج.م";

            if (TxtTotalExpenses != null)
                TxtTotalExpenses.Text = $"{totalExpenses:N0} ج.م";

            if (TxtNetProfit != null)
                TxtNetProfit.Text = $"{Math.Abs(netProfit):N0} ج.م";

            // تحديث الألوان والأيقونات حسب الربح/الخسارة
            if (netProfit >= 0)
            {
                // ربح - اللون الأخضر
                UpdateProfitDisplay(true, netProfit);
            }
            else
            {
                // خسارة - اللون الأحمر
                UpdateProfitDisplay(false, netProfit);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث واجهة المستخدم المالية: {ex.Message}");
        }
    }

    /// <summary>
    /// تحديث عرض الربح/الخسارة
    /// </summary>
    private void UpdateProfitDisplay(bool isProfit, decimal amount)
    {
        try
        {
            if (isProfit)
            {
                // ربح - أخضر
                if (TxtProfitLabel != null)
                {
                    TxtProfitLabel.Text = "صافي الربح";
                    TxtProfitLabel.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x2E, 0x7D, 0x32));
                }

                if (TxtNetProfit != null)
                    TxtNetProfit.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x1B, 0x5E, 0x20));

                if (TxtProfitIcon != null)
                    TxtProfitIcon.Text = "💰";

                if (TxtProfitArrow != null)
                    TxtProfitArrow.Text = "🔺";

                // تحديث خلفية البطاقة للأخضر
                if (BorderNetProfit != null)
                {
                    BorderNetProfit.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xE8, 0xF5, 0xE9));
                    BorderNetProfit.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x4C, 0xAF, 0x50));
                }
            }
            else
            {
                // خسارة - أحمر
                if (TxtProfitLabel != null)
                {
                    TxtProfitLabel.Text = "صافي الخسارة";
                    TxtProfitLabel.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xC6, 0x28, 0x28));
                }

                if (TxtNetProfit != null)
                    TxtNetProfit.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xB7, 0x1C, 0x1C));

                if (TxtProfitIcon != null)
                    TxtProfitIcon.Text = "💸";

                if (TxtProfitArrow != null)
                    TxtProfitArrow.Text = "🔻";

                // تحديث خلفية البطاقة للأحمر
                if (BorderNetProfit != null)
                {
                    BorderNetProfit.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xFF, 0xEB, 0xEE));
                    BorderNetProfit.BorderBrush = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0xF4, 0x43, 0x36));
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض الربح/الخسارة: {ex.Message}");
        }
    }

    /// <summary>
    /// تحميل الملخص السريع
    /// </summary>
    private async System.Threading.Tasks.Task LoadQuickSummary()
    {
        try
        {
            if (_context?.CustomerOrders == null) return;

            var (startDate, endDate) = GetPeriodDates();

            // آخر طلب في الفترة المحددة
            var lastOrder = await _context.CustomerOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .OrderByDescending(o => o.OrderDate)
                .FirstOrDefaultAsync();

            if (TxtLastOrderDate != null)
            {
                if (lastOrder != null)
                    TxtLastOrderDate.Text = lastOrder.OrderDate.ToString("dd/MM/yyyy");
                else
                    TxtLastOrderDate.Text = "لا توجد طلبات في هذه الفترة";
            }

            // آخر دفعة في الفترة المحددة
            var lastPayment = await _context.CustomerPayments
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .OrderByDescending(p => p.PaymentDate)
                .FirstOrDefaultAsync();

            if (TxtLastPaymentDate != null)
            {
                if (lastPayment != null)
                    TxtLastPaymentDate.Text = lastPayment.PaymentDate.ToString("dd/MM/yyyy");
                else
                    TxtLastPaymentDate.Text = "لا توجد دفعات في هذه الفترة";
            }

            // متوسط قيمة الطلب في الفترة المحددة
            var orders = await _context.CustomerOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .ToListAsync();

            if (TxtAverageOrderValue != null)
            {
                if (orders.Any())
                {
                    var averageValue = orders.Average(o => o.TotalAmount);
                    TxtAverageOrderValue.Text = $"{averageValue:N0} ج.م";
                }
                else
                {
                    TxtAverageOrderValue.Text = "0 ج.م";
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحميل الملخص السريع: {ex.Message}");

            if (TxtLastOrderDate != null) TxtLastOrderDate.Text = "غير متوفر";
            if (TxtLastPaymentDate != null) TxtLastPaymentDate.Text = "غير متوفر";
            if (TxtAverageOrderValue != null) TxtAverageOrderValue.Text = "0 ج.م";
        }
    }

    /// <summary>
    /// تحميل التقرير بصيغة PDF
    /// </summary>
    private async void BtnDownloadReport_Click(object sender, RoutedEventArgs e)
    {
        Window? loadingWindow = null;
        try
        {
            // إظهار رسالة تحميل
            loadingWindow = new Window
            {
                Title = "جاري إنشاء التقرير...",
                Content = new StackPanel
                {
                    Children =
                    {
                        new TextBlock
                        {
                            Text = "📊 جاري إنشاء تقرير PDF...",
                            FontSize = 16,
                            FontWeight = FontWeights.Bold,
                            HorizontalAlignment = System.Windows.HorizontalAlignment.Center,
                            Margin = new Thickness(20)
                        }
                    }
                },
                Width = 300,
                Height = 100,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = Window.GetWindow(this),
                ResizeMode = ResizeMode.NoResize
            };

            loadingWindow.Show();

            // إنشاء التقرير
            await GenerateReportPDF();

            loadingWindow.Close();
            loadingWindow = null;
        }
        catch (Exception ex)
        {
            loadingWindow?.Close();
            MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// إنشاء تقرير PDF
    /// </summary>
    private async System.Threading.Tasks.Task GenerateReportPDF()
    {
        try
        {
            if (_context == null)
            {
                throw new Exception("قاعدة البيانات غير متاحة");
            }

            var (startDate, endDate) = GetPeriodDates();
            var periodName = GetPeriodName();

            // إظهار نافذة حفظ الملف
            var saveDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "PDF Files (*.pdf)|*.pdf",
                FileName = $"تقرير_{periodName}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf",
                Title = "حفظ تقرير PDF",
                DefaultExt = "pdf",
                AddExtension = true
            };

            if (saveDialog.ShowDialog() != true)
            {
                return; // المستخدم ألغى العملية
            }

            // الحصول على البيانات للفترة المحددة
            var totalOrders = await _context.CustomerOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .CountAsync();
            var totalCustomers = await _context.Customers.CountAsync(c => c.IsActive);
            var totalPayments = await _context.CustomerPayments
                .Where(p => p.PaymentDate >= startDate && p.PaymentDate <= endDate)
                .SumAsync(p => p.Amount);

            // ✅ البيانات المالية المصححة (حسب النوع وليس الفئة)
            // حساب الإيرادات من حركات الخزينة (حسب النوع: Income)
            var cashRevenue = await _context.CashTransactions
                .Where(t => t.TransactionDate >= startDate &&
                           t.TransactionDate <= endDate &&
                           t.Type == Models.TransactionType.Income)  // ✅ جميع عمليات القبض
                .SumAsync(t => t.Amount);

            // حساب المصروفات من حركات الخزينة (حسب النوع: Expense)
            var cashExpenses = await _context.CashTransactions
                .Where(t => t.TransactionDate >= startDate &&
                           t.TransactionDate <= endDate &&
                           t.Type == Models.TransactionType.Expense)  // ✅ جميع عمليات الصرف
                .SumAsync(t => t.Amount);

            // إضافة الطلبات والفواتير
            var ordersRevenue = await _context.CustomerOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .SumAsync(o => o.TotalAmount);
            var salesInvoicesRevenue = await _context.Invoices
                .Where(i => i.InvoiceDate >= startDate && i.InvoiceDate <= endDate && i.Type == InvoiceType.Sales)
                .SumAsync(i => i.TotalAmount);
            var purchaseInvoicesExpenses = await _context.Invoices
                .Where(i => i.InvoiceDate >= startDate && i.InvoiceDate <= endDate && i.Type == InvoiceType.Purchase)
                .SumAsync(i => i.TotalAmount);

            // الإجمالي النهائي
            var totalRevenue = cashRevenue + ordersRevenue + salesInvoicesRevenue;
            var totalExpenses = cashExpenses + purchaseInvoicesExpenses;
            var netProfit = totalRevenue - totalExpenses;

            // طباعة تفاصيل للتشخيص
            System.Diagnostics.Debug.WriteLine($"=== تفاصيل التقرير المالي (PDF) - مُصحح ===");
            System.Diagnostics.Debug.WriteLine($"الفترة: {startDate:yyyy-MM-dd} إلى {endDate:yyyy-MM-dd}");
            System.Diagnostics.Debug.WriteLine($"✅ إيرادات الخزينة (Type=Income): {cashRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إيرادات الطلبات: {ordersRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إيرادات فواتير المبيعات: {salesInvoicesRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إجمالي الإيرادات: {totalRevenue:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"✅ مصروفات الخزينة (Type=Expense): {cashExpenses:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"مصروفات فواتير المشتريات: {purchaseInvoicesExpenses:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"إجمالي المصروفات: {totalExpenses:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"صافي الربح/الخسارة: {netProfit:N0} ج.م");
            System.Diagnostics.Debug.WriteLine($"=== نهاية تفاصيل PDF المُصحح ===");

            var orders = await _context.CustomerOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .Include(o => o.Customer)
                .OrderByDescending(o => o.OrderDate)
                .Take(10)
                .ToListAsync();

            // إنشاء ملف PDF مع دعم RTL
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.DefaultTextStyle(x => x.FontSize(12).FontFamily("Arial"));

                    page.Header()
                        .AlignRight()
                        .Text($"📊 تقرير إحصائيات المصنع - {periodName}")
                        .SemiBold().FontSize(18).FontColor(Colors.Blue.Medium);

                    page.Content()
                        .PaddingVertical(1, Unit.Centimetre)
                        .Column(column =>
                        {
                            column.Spacing(20);

                            // معلومات التقرير
                            column.Item().AlignRight().Text($"تاريخ التقرير: {DateTime.Now:dd/MM/yyyy HH:mm}")
                                .FontSize(10).FontColor(Colors.Grey.Medium);

                            column.Item().AlignRight().Text($"الفترة: من {startDate:dd/MM/yyyy} إلى {endDate:dd/MM/yyyy}")
                                .FontSize(10).FontColor(Colors.Grey.Medium);

                            // الإحصائيات الرئيسية
                            column.Item().AlignRight().Text("الإحصائيات الرئيسية").SemiBold().FontSize(16);

                            column.Item().Row(row =>
                            {
                                row.RelativeItem().AlignRight().Text($"إجمالي الطلبات: {totalOrders:N0}");
                                row.RelativeItem().AlignRight().Text($"عدد العملاء: {totalCustomers:N0}");
                            });

                            column.Item().AlignRight().Text($"إجمالي المدفوعات: {totalPayments:N0} ج.م").SemiBold();

                            // الملخص المالي الشامل
                            column.Item().AlignRight().Text("الملخص المالي الشامل").SemiBold().FontSize(16);

                            column.Item().Row(row =>
                            {
                                row.RelativeItem().AlignRight().Text($"إجمالي الإيرادات: {totalRevenue:N0} ج.م").FontColor(Colors.Green.Medium);
                                row.RelativeItem().AlignRight().Text($"إجمالي المصروفات: {totalExpenses:N0} ج.م").FontColor(Colors.Red.Medium);
                            });

                            var profitText = netProfit >= 0 ? $"صافي الربح: {netProfit:N0} ج.م ✅" : $"صافي الخسارة: {Math.Abs(netProfit):N0} ج.م 🚨";
                            var profitColor = netProfit >= 0 ? Colors.Green.Darken2 : Colors.Red.Darken2;
                            column.Item().AlignRight().Text(profitText).SemiBold().FontColor(profitColor);

                            // جدول الطلبات الحديثة
                            if (orders.Any())
                            {
                                column.Item().AlignRight().Text($"آخر {orders.Count} طلبات في {periodName}").SemiBold().FontSize(14);

                                column.Item().Table(table =>
                                {
                                    table.ColumnsDefinition(columns =>
                                    {
                                        columns.RelativeColumn(); // المبلغ
                                        columns.RelativeColumn(); // التاريخ
                                        columns.RelativeColumn(); // العميل
                                        columns.RelativeColumn(); // رقم الطلب
                                    });

                                    table.Header(header =>
                                    {
                                        header.Cell().Element(CellStyleRTL).AlignRight().Text("المبلغ");
                                        header.Cell().Element(CellStyleRTL).AlignRight().Text("التاريخ");
                                        header.Cell().Element(CellStyleRTL).AlignRight().Text("العميل");
                                        header.Cell().Element(CellStyleRTL).AlignRight().Text("رقم الطلب");
                                    });

                                    foreach (var order in orders)
                                    {
                                        table.Cell().Element(CellStyleRTL).AlignRight().Text($"{order.TotalAmount:N0} ج.م");
                                        table.Cell().Element(CellStyleRTL).AlignRight().Text(order.OrderDate.ToString("dd/MM/yyyy"));
                                        table.Cell().Element(CellStyleRTL).AlignRight().Text(order.Customer?.Name ?? "غير محدد");
                                        table.Cell().Element(CellStyleRTL).AlignRight().Text(order.OrderNumber);
                                    }
                                });
                            }
                            else
                            {
                                column.Item().AlignRight().Text($"لا توجد طلبات في {periodName}").FontSize(14).FontColor(Colors.Grey.Medium);
                            }
                        });

                    page.Footer()
                        .AlignCenter()
                        .Text(x =>
                        {
                            x.Span("تم إنشاء التقرير بواسطة نظام إدارة المصنع - صفحة ");
                            x.CurrentPageNumber();
                        });
                });
            });

            // حفظ الملف في المسار المحدد من قبل المستخدم
            var filePath = saveDialog.FileName;

            // التأكد من أن الملف له امتداد .pdf صحيح
            if (!filePath.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
            {
                filePath += ".pdf";
            }

            document.GeneratePdf(filePath);

            // عرض رسالة نجاح مع خيار فتح الملف
            var result = MessageBox.Show(
                $"تم إنشاء التقرير بنجاح!\n\nالمسار: {filePath}\n\nهل تريد فتح الملف الآن؟",
                "تم الحفظ بنجاح",
                MessageBoxButton.YesNo,
                MessageBoxImage.Information);

            if (result == MessageBoxResult.Yes)
            {
                OpenPdfFile(filePath);
            }
        }
        catch (Exception ex)
        {
            throw new Exception($"فشل في إنشاء تقرير PDF: {ex.Message}");
        }
    }

    /// <summary>
    /// الحصول على اسم الفترة المحددة
    /// </summary>
    private string GetPeriodName()
    {
        return _currentPeriod switch
        {
            ReportPeriodType.Daily => "يومي",
            ReportPeriodType.Weekly => "أسبوعي",
            ReportPeriodType.Monthly => "شهري",
            ReportPeriodType.Yearly => "سنوي",
            _ => "يومي"
        };
    }

    /// <summary>
    /// تنسيق خلايا الجدول مع دعم RTL
    /// </summary>
    private static IContainer CellStyleRTL(IContainer container)
    {
        return container.DefaultTextStyle(x => x.FontSize(10))
            .PaddingVertical(8).PaddingHorizontal(5)
            .BorderBottom(1).BorderColor(Colors.Grey.Lighten2);
    }

    /// <summary>
    /// تنسيق خلايا الجدول (للتوافق مع الكود القديم)
    /// </summary>
    private static IContainer CellStyle(IContainer container)
    {
        return CellStyleRTL(container);
    }

    /// <summary>
    /// فتح ملف PDF بطريقة آمنة ومتوافقة مع جميع أجهزة Windows
    /// </summary>
    /// <param name="filePath">مسار ملف PDF</param>
    private void OpenPdfFile(string filePath)
    {
        try
        {
            // التأكد من وجود الملف
            if (!File.Exists(filePath))
            {
                MessageBox.Show("الملف غير موجود!", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // استخدام ProcessStartInfo للتوافق الأمثل
            var startInfo = new ProcessStartInfo
            {
                FileName = filePath,
                UseShellExecute = true
            };
            Process.Start(startInfo);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"تعذر فتح الملف: {ex.Message}\n\nيمكنك العثور على الملف في:\n{filePath}",
                "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
