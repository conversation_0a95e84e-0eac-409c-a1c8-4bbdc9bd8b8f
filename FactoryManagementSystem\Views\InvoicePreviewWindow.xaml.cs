using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.ViewModels;
using FactoryManagementSystem.Helpers;
using Microsoft.Win32;
using System.IO;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.Windows.Media.Imaging;
using QuestColors = QuestPDF.Helpers.Colors;

namespace FactoryManagementSystem.Views;

public partial class InvoicePreviewWindow : Window
{
    private readonly Invoice _invoice;
    private readonly List<InvoiceOrderViewModel>? _orderItems;
    private readonly FactoryDbContext _context;

    // Constructor للفاتورة المحفوظة
    public InvoicePreviewWindow(Invoice invoice)
    {
        InitializeComponent();
        _invoice = invoice ?? throw new ArgumentNullException(nameof(invoice));
        _context = new FactoryDbContext();
        _orderItems = null;
        LoadInvoiceData();
        LoadOrderItemsFromDb();
    }

    // Constructor للمعاينة المؤقتة
    public InvoicePreviewWindow(Invoice invoice, List<InvoiceOrderViewModel> orderItems)
    {
        InitializeComponent();
        _invoice = invoice ?? throw new ArgumentNullException(nameof(invoice));
        _orderItems = orderItems ?? throw new ArgumentNullException(nameof(orderItems));
        _context = new FactoryDbContext();
        LoadInvoiceData();
        LoadOrderItems();
    }

    private async void LoadInvoiceData()
    {
        try
        {
            // تحميل بيانات الفاتورة
            TxtInvoiceNumber.Text = _invoice.InvoiceNumber;
            TxtInvoiceDate.Text = _invoice.InvoiceDate.ToString("yyyy/MM/dd");
            TxtDueDate.Text = _invoice.DueDate?.ToString("yyyy/MM/dd") ?? "غير محدد";
            TxtInvoiceNotes.Text = string.IsNullOrWhiteSpace(_invoice.Notes) ? "لا توجد ملاحظات" : _invoice.Notes;
            
            // تحميل بيانات العميل
            if (_invoice.CustomerId.HasValue)
            {
                var customer = await _context.Customers.FindAsync(_invoice.CustomerId.Value);
                if (customer != null)
                {
                    // TxtCustomerName.Text = customer.Name; // Removed as per edit hint
                    // TxtCustomerPhone.Text = customer.Phone ?? "غير محدد"; // Removed as per edit hint
                    // TxtCustomerAddress.Text = customer.Address ?? "غير محدد"; // Removed as per edit hint
                    // TxtTaxNumber.Text = customer.TaxNumber ?? "غير محدد"; // Removed as per edit hint
                }
            }

            // تحميل المبالغ
            // TxtSubTotal.Text = $"{_invoice.SubTotal:N2} ج.م"; // Removed as per edit hint
            // TxtTaxAmount.Text = $"{_invoice.TaxAmount:N2} ج.م ({_invoice.TaxRate:N1}%)"; // Removed as per edit hint
            TxtTotalAmount.Text = $"{_invoice.TotalAmount:N2} ج.م";
            
            // تاريخ الطباعة بالتوقيت المصري
            TxtPrintDate.Text = $"تم الطباعة في: {EgyptTimeHelper.ToString("yyyy/MM/dd HH:mm")}";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الفاتورة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadOrderItems()
    {
        if (_orderItems != null)
        {
            DgInvoiceItems.ItemsSource = _orderItems;
            UpdateSummaryFields(_orderItems);
        }
    }

    private void LoadOrderItemsFromDb()
    {
        // جلب الطلبات المرتبطة بالفاتورة من قاعدة البيانات
        var orderItems = _context.InvoiceCustomerOrders
            .Include(x => x.Order)
            .Where(x => x.InvoiceId == _invoice.InvoiceId)
            .Select(x => new InvoiceOrderViewModel
            {
                OrderId = x.Order.OrderId,
                OrderNumber = x.Order.OrderNumber,
                OrderDate = x.Order.OrderDate,
                FabricType = x.Order.FabricType,
                Color = x.Order.Color ?? "",
                Quantity = x.Order.Quantity,
                PricePerMeter = x.Order.PricePerMeter,
                TotalAmount = x.Order.TotalAmount,
                PaidAmount = x.Order.PaidAmount,
                RemainingAmount = x.Order.RemainingAmount,
                StatusText = x.Order.Status.ToString()
            })
            .OrderBy(x => x.OrderDate)
            .ToList();
        DgInvoiceItems.ItemsSource = orderItems;
        UpdateSummaryFields(orderItems);
    }

    private void UpdateSummaryFields(List<InvoiceOrderViewModel> items)
    {
        try
        {
            // إجمالي الكمية
            var totalQuantity = items.Sum(x => x.Quantity);
            TxtTotalQuantity.Text = totalQuantity.ToString("N2");

            // إجمالي قيمة الطلبات الفعلية (الكمية × السعر)
            var invoiceAmount = items.Sum(x => x.Quantity * x.PricePerMeter);

            // إجمالي المدفوع من هذه الطلبات
            var totalPaidFromOrders = items.Sum(x => x.PaidAmount);

            // حساب رصيد العميل
            decimal previousBalance = 0;
            decimal currentBalance = 0;
            decimal totalPaidToCustomer = 0;

            if (_invoice.CustomerId.HasValue)
            {
                var customer = _context.Customers.FirstOrDefault(c => c.CustomerId == _invoice.CustomerId.Value);
                if (customer != null)
                {
                    // الرصيد الحالي للعميل (بعد كل المعاملات)
                    currentBalance = customer.Balance;

                    // إجمالي المدفوعات من العميل (من جدول المدفوعات)
                    totalPaidToCustomer = _context.CustomerPayments
                        .Where(p => p.CustomerId == customer.CustomerId)
                        .Sum(p => (decimal?)p.Amount) ?? 0;

                    // الرصيد السابق = الرصيد الحالي - إجمالي هذه الفاتورة + المدفوع
                    previousBalance = currentBalance - invoiceAmount + totalPaidToCustomer;

                    // إعادة حساب الرصيد الحالي = الرصيد السابق + إجمالي الفاتورة - المدفوع
                    currentBalance = previousBalance + invoiceAmount - totalPaidToCustomer;
                }
            }

            // عرض القيم
            TxtTotalAmount.Text = invoiceAmount.ToString("N2");
            TxtTotalPaidInTable.Text = totalPaidFromOrders.ToString("N2");
            TxtPreviousBalance.Text = previousBalance.ToString("N2") + " ج.م";
            TxtNetBalance.Text = currentBalance.ToString("N2") + " ج.م";
            TxtTotalPaid.Text = totalPaidToCustomer.ToString("N2") + " ج.م";
            TxtInvoiceTotal.Text = invoiceAmount.ToString("N2") + " ج.م";

            // تحديث إجمالي الفاتورة في النموذج
            _invoice.TotalAmount = invoiceAmount;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حساب الإجماليات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnPrint_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إنشاء الفاتورة للطباعة
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(1, Unit.Centimetre);
                    page.PageColor(QuestColors.White);
                    page.DefaultTextStyle(x => x.FontSize(10).FontFamily("Tahoma").Bold().DirectionFromRightToLeft());

                    page.Content().Column(column =>
                    {
                        // رأس الفاتورة - مطابق للورقة
                        column.Item().AlignCenter().Text("مصنع مستر شيكو")
                            .FontSize(18).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        column.Item().AlignCenter().Text("فاتورة مبيعات رقم 1")
                            .FontSize(16).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        column.Item().PaddingVertical(10);

                        // معلومات العميل والفاتورة - مطابق للورقة
                        column.Item().Table(headerTable =>
                        {
                            headerTable.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(1); // معلومات العميل
                                columns.RelativeColumn(1); // معلومات الفاتورة
                            });

                            // معلومات العميل (الجانب الأيسر)
                            if (_invoice.CustomerId.HasValue)
                            {
                                var customer = _context.Customers.FirstOrDefault(c => c.CustomerId == _invoice.CustomerId.Value);
                                if (customer != null)
                                {
                                    headerTable.Cell().Element(CellStyle).Padding(5).Column(customerColumn =>
                                    {
                                        customerColumn.Item().Text($"اسم العميل: {customer.Name}")
                                            .FontSize(10).Bold().DirectionFromRightToLeft();
                                        customerColumn.Item().Text($"الهاتف: {customer.Phone ?? "غير محدد"}")
                                            .FontSize(10).DirectionFromRightToLeft();
                                        customerColumn.Item().Text($"العنوان: {customer.Address ?? "غير محدد"}")
                                            .FontSize(10).DirectionFromRightToLeft();
                                    });
                                }
                                else
                                {
                                    headerTable.Cell().Element(CellStyle).Padding(5).Text("معلومات العميل غير متوفرة")
                                        .FontSize(10).DirectionFromRightToLeft();
                                }
                            }
                            else
                            {
                                headerTable.Cell().Element(CellStyle).Padding(5).Text("لا يوجد عميل محدد")
                                    .FontSize(10).DirectionFromRightToLeft();
                            }

                            // معلومات الفاتورة (الجانب الأيمن)
                            headerTable.Cell().Element(CellStyle).Padding(5).Column(invoiceColumn =>
                            {
                                invoiceColumn.Item().Text($"رقم: {_invoice.InvoiceNumber}")
                                    .FontSize(10).Bold().DirectionFromRightToLeft();
                                invoiceColumn.Item().Text($"التاريخ: {_invoice.InvoiceDate:dd/MM/yyyy}")
                                    .FontSize(10).DirectionFromRightToLeft();
                                invoiceColumn.Item().Text($"تاريخ الاستحقاق: {(_invoice.DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد")}")
                                    .FontSize(10).DirectionFromRightToLeft();
                            });
                        });

                        column.Item().PaddingVertical(10);

                        // جدول الطلبات - مطابق للورقة
                        var orderItems = _orderItems ?? GetOrderItemsFromDb();

                        column.Item().Table(table =>
                        {
                            table.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(2); // المبلغ
                                columns.RelativeColumn(2); // السعر
                                columns.RelativeColumn(1); // الوحدة
                                columns.RelativeColumn(2); // نوع القماش
                                columns.RelativeColumn(2); // اللون
                                columns.RelativeColumn(2); // الكمية
                                columns.RelativeColumn(2); // التاريخ
                                columns.RelativeColumn(2); // رقم الطلب
                            });

                            // رأس الجدول - مطابق للورقة
                            table.Header(header =>
                            {
                                header.Cell().Element(CellStyle).AlignCenter().Text("المبلغ").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("السعر").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("الوحدة").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("نوع القماش").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("اللون").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("الكمية").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("التاريخ").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("رقم الطلب").FontSize(9).Bold().DirectionFromRightToLeft();
                            });

                            // بيانات الجدول - مطابق للورقة مع المبالغ الحقيقية
                            foreach (var item in orderItems)
                            {
                                // المبلغ الحقيقي = الكمية × السعر
                                var actualAmount = item.Quantity * item.PricePerMeter;

                                table.Cell().Element(CellStyle).AlignCenter().Text($"{actualAmount:N2}").FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.PricePerMeter:N2}").FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text("متر").FontSize(9).DirectionFromRightToLeft();
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.FabricType).FontSize(9).DirectionFromRightToLeft();
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.Color).FontSize(9).DirectionFromRightToLeft();
                                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.Quantity:N2}").FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.OrderDate.ToString("dd/MM/yyyy")).FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.OrderNumber).FontSize(9);
                            }
                        });

                        column.Item().PaddingVertical(10);

                        // الملخص المالي - حسابات حقيقية
                        var invoiceAmount = orderItems.Sum(x => x.Quantity * x.PricePerMeter);
                        decimal previousBalance = 0;
                        decimal currentBalance = 0;
                        decimal totalPaidToCustomer = 0;

                        if (_invoice.CustomerId.HasValue)
                        {
                            var customer = _context.Customers.FirstOrDefault(c => c.CustomerId == _invoice.CustomerId.Value);
                            if (customer != null)
                            {
                                currentBalance = customer.Balance;
                                previousBalance = currentBalance - invoiceAmount;
                            }
                        }

                        // عنوان رصيد العميل
                        column.Item().PaddingTop(20).AlignCenter().Text("رصيد العميل")
                            .FontSize(16).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        // ملخص الحساب - يمتد على مستوى الصفحة
                        column.Item().PaddingTop(10).BorderColor(QuestColors.Black).Border(1).Table(summaryTable =>
                        {
                            summaryTable.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(2); // القيم
                                columns.RelativeColumn(3); // البيانات
                            });

                            // رصيد السابق (حقيقي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{previousBalance:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("رصيد السابق")
                                .FontSize(10).DirectionFromRightToLeft();

                            // الإجمالي (ثمن البضاعة الفعلي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{invoiceAmount:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("الإجمالي")
                                .FontSize(10).DirectionFromRightToLeft();

                            // مدفوع من العميل (حقيقي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{totalPaidToCustomer:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("مدفوع من العميل")
                                .FontSize(10).DirectionFromRightToLeft();

                            // الرصيد الحالي (حقيقي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{currentBalance:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("الرصيد الحالي")
                                .FontSize(10).DirectionFromRightToLeft();
                        });

                        // النص في أسفل الصفحة - مطابق للورقة
                        column.Item().PaddingTop(20).AlignCenter().Text("سياسة الشركة: لا يتم استرداد البضاعة بعد مرور أكثر من أسبوعين من تاريخ الشراء")
                            .FontSize(9).Bold().DirectionFromRightToLeft();

                        column.Item().PaddingTop(10).AlignCenter().Text("مدفوعة")
                            .FontSize(12).Bold().DirectionFromRightToLeft();
                    });
                });
            });

            // طباعة الفاتورة مباشرة
            var printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                // تحويل الفاتورة إلى PDF
                var pdfBytes = document.GeneratePdf();

                // إنشاء مسار آمن للملف المؤقت
                var tempDir = Path.GetTempPath();
                var tempFileName = $"فاتورة_{_invoice.InvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}.pdf";
                var tempPath = Path.Combine(tempDir, tempFileName);

                // حفظ الملف المؤقت
                File.WriteAllBytes(tempPath, pdfBytes);

                // طباعة الملف بطريقة آمنة
                PrintPdfFile(tempPath);

                // حذف الملف المؤقت بعد فترة
                Task.Delay(10000).ContinueWith(_ =>
                {
                    try
                    {
                        if (File.Exists(tempPath))
                            File.Delete(tempPath);
                    }
                    catch { /* تجاهل أخطاء الحذف */ }
                });
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSavePdf_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // إنشاء اسم ملف آمن
            var safeFileName = $"فاتورة_{_invoice.InvoiceNumber}_{EgyptTimeHelper.Now:yyyyMMdd}.pdf";

            var saveDialog = new SaveFileDialog
            {
                Filter = "PDF Files (*.pdf)|*.pdf",
                FileName = safeFileName,
                DefaultExt = "pdf",
                AddExtension = true
            };

            if (saveDialog.ShowDialog() == true)
            {
                // التأكد من أن الملف له امتداد .pdf صحيح
                var finalPath = saveDialog.FileName;
                if (!finalPath.EndsWith(".pdf", StringComparison.OrdinalIgnoreCase))
                {
                    finalPath += ".pdf";
                }

                // تحويل النافذة إلى PDF باستخدام QuestPDF
                GenerateQuestPdf(finalPath);

                // عرض رسالة نجاح مع خيار فتح الملف
                var result = MessageBox.Show(
                    $"تم حفظ الفاتورة بنجاح!\n\nالمسار: {finalPath}\n\nهل تريد فتح الملف الآن؟",
                    "تم الحفظ بنجاح",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    OpenPdfFile(finalPath);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الحفظ: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// طباعة ملف PDF بطريقة آمنة ومتوافقة مع جميع أجهزة Windows
    /// </summary>
    /// <param name="pdfPath">مسار ملف PDF</param>
    private void PrintPdfFile(string pdfPath)
    {
        try
        {
            // التأكد من وجود الملف
            if (!File.Exists(pdfPath))
            {
                MessageBox.Show("الملف المؤقت غير موجود!", "خطأ في الطباعة",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // استخدام ProcessStartInfo للطباعة الآمنة
            var psi = new System.Diagnostics.ProcessStartInfo
            {
                FileName = pdfPath,
                UseShellExecute = true,
                Verb = "print",
                CreateNoWindow = true,
                WindowStyle = System.Diagnostics.ProcessWindowStyle.Hidden
            };

            System.Diagnostics.Process.Start(psi);

            MessageBox.Show("تم إرسال الفاتورة للطباعة بنجاح!", "طباعة",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (System.ComponentModel.Win32Exception ex)
        {
            // خطأ في النظام - عادة يعني عدم وجود برنامج PDF أو طابعة
            MessageBox.Show(
                $"لا يمكن طباعة الملف تلقائيًا.\n\n" +
                $"الأسباب المحتملة:\n" +
                $"• لا يوجد برنامج افتراضي لفتح ملفات PDF\n" +
                $"• لا توجد طابعة مثبتة أو متاحة\n\n" +
                $"الحلول:\n" +
                $"1. تأكد من تثبيت برنامج PDF (مثل Adobe Reader)\n" +
                $"2. تأكد من وجود طابعة مثبتة\n" +
                $"3. أو افتح الملف يدويًا واطبعه من:\n{pdfPath}\n\n" +
                $"تفاصيل الخطأ: {ex.Message}",
                "تعذرت الطباعة",
                MessageBoxButton.OK,
                MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            // أخطاء أخرى
            MessageBox.Show(
                $"حدث خطأ أثناء محاولة طباعة الملف.\n\n" +
                $"يمكنك فتح الملف يدويًا وطباعته من:\n{pdfPath}\n\n" +
                $"تفاصيل الخطأ: {ex.Message}",
                "خطأ في الطباعة",
                MessageBoxButton.OK,
                MessageBoxImage.Warning);
        }
    }

    /// <summary>
    /// فتح ملف PDF بطريقة آمنة ومتوافقة مع جميع أجهزة Windows
    /// </summary>
    /// <param name="pdfPath">مسار ملف PDF</param>
    private void OpenPdfFile(string pdfPath)
    {
        try
        {
            // التأكد من وجود الملف
            if (!File.Exists(pdfPath))
            {
                MessageBox.Show("الملف غير موجود!", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // استخدام ProcessStartInfo للتوافق الأمثل
            var psi = new System.Diagnostics.ProcessStartInfo
            {
                FileName = pdfPath,
                UseShellExecute = true,
                Verb = "open"
            };

            System.Diagnostics.Process.Start(psi);
        }
        catch (System.ComponentModel.Win32Exception ex)
        {
            // خطأ في النظام - عادة يعني عدم وجود برنامج PDF
            MessageBox.Show(
                $"لا يمكن فتح الملف تلقائيًا.\n\n" +
                $"السبب المحتمل: لا يوجد برنامج افتراضي لفتح ملفات PDF.\n\n" +
                $"الحلول:\n" +
                $"1. قم بتثبيت برنامج لعرض ملفات PDF (مثل Adobe Reader)\n" +
                $"2. أو افتح الملف يدويًا من المسار:\n{pdfPath}\n\n" +
                $"تفاصيل الخطأ: {ex.Message}",
                "تعذر فتح الملف",
                MessageBoxButton.OK,
                MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            // أخطاء أخرى
            MessageBox.Show(
                $"حدث خطأ أثناء محاولة فتح الملف.\n\n" +
                $"يمكنك فتح الملف يدويًا من المسار:\n{pdfPath}\n\n" +
                $"تفاصيل الخطأ: {ex.Message}",
                "خطأ في فتح الملف",
                MessageBoxButton.OK,
                MessageBoxImage.Warning);
        }
    }

    private void GenerateQuestPdf(string fileName)
    {
        try
        {
            // تهيئة QuestPDF
            QuestPDF.Settings.License = LicenseType.Community;

            // إنشاء مستند PDF جديد
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(QuestColors.White);
                    page.DefaultTextStyle(x => x.FontSize(10).FontFamily("Tahoma").Bold().DirectionFromRightToLeft());

                    page.Content().Column(column =>
                    {
                        // رأس الفاتورة - مطابق للورقة
                        column.Item().AlignCenter().Text("مصنع مستر شيكو")
                            .FontSize(18).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        column.Item().AlignCenter().Text("فاتورة مبيعات رقم 1")
                            .FontSize(16).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        column.Item().PaddingVertical(10);

                        // معلومات الفاتورة والعميل في جدول - مطابق للورقة
                        column.Item().Table(headerTable =>
                        {
                            headerTable.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(2); // معلومات العميل
                                columns.RelativeColumn(1); // معلومات الفاتورة
                            });

                            // معلومات العميل (الجانب الأيسر)
                            if (_invoice.CustomerId.HasValue)
                            {
                                var customer = _context.Customers.FirstOrDefault(c => c.CustomerId == _invoice.CustomerId.Value);
                                if (customer != null)
                                {
                                    headerTable.Cell().Element(CellStyle).Padding(5).Column(customerColumn =>
                                    {
                                        customerColumn.Item().Text($"اسم العميل: {customer.Name}")
                                            .FontSize(10).Bold().DirectionFromRightToLeft();
                                        customerColumn.Item().Text($"الهاتف: {customer.Phone ?? "غير محدد"}")
                                            .FontSize(10).DirectionFromRightToLeft();
                                        customerColumn.Item().Text($"العنوان: {customer.Address ?? "غير محدد"}")
                                            .FontSize(10).DirectionFromRightToLeft();
                                    });
                                }
                                else
                                {
                                    headerTable.Cell().Element(CellStyle).Padding(5).Text("معلومات العميل غير متوفرة")
                                        .FontSize(12).DirectionFromRightToLeft();
                                }
                            }
                            else
                            {
                                headerTable.Cell().Element(CellStyle).Padding(5).Text("لا يوجد عميل محدد")
                                    .FontSize(12).DirectionFromRightToLeft();
                            }

                            // معلومات الفاتورة (الجانب الأيمن)
                            headerTable.Cell().Element(CellStyle).Padding(5).Column(invoiceColumn =>
                            {
                                invoiceColumn.Item().Text($"رقم: {_invoice.InvoiceNumber}")
                                    .FontSize(10).Bold().DirectionFromRightToLeft();
                                invoiceColumn.Item().Text($"التاريخ: {_invoice.InvoiceDate:dd/MM/yyyy}")
                                    .FontSize(10).DirectionFromRightToLeft();
                                invoiceColumn.Item().Text($"تاريخ الاستحقاق: {(_invoice.DueDate?.ToString("dd/MM/yyyy") ?? "غير محدد")}")
                                    .FontSize(10).DirectionFromRightToLeft();
                            });
                        });

                        column.Item().PaddingVertical(10);



                        // جدول الطلبات - مطابق للورقة
                        var orderItems = _orderItems ?? GetOrderItemsFromDb();

                        column.Item().Table(table =>
                        {
                            // ترتيب الأعمدة مطابق للورقة: المبلغ، السعر، الوحدة، نوع القماش، اللون، الكمية، التاريخ، رقم الطلب
                            table.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(2); // المبلغ
                                columns.RelativeColumn(2); // السعر
                                columns.RelativeColumn(1.5f); // الوحدة
                                columns.RelativeColumn(2.5f); // نوع القماش
                                columns.RelativeColumn(2); // اللون
                                columns.RelativeColumn(1.5f); // الكمية
                                columns.RelativeColumn(2); // التاريخ
                                columns.RelativeColumn(2); // رقم الطلب
                            });

                            // رأس الجدول - مطابق للورقة
                            table.Header(header =>
                            {
                                header.Cell().Element(CellStyle).AlignCenter().Text("المبلغ").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("السعر").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("الوحدة").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("نوع القماش").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("اللون").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("الكمية").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("التاريخ").FontSize(9).Bold().DirectionFromRightToLeft();
                                header.Cell().Element(CellStyle).AlignCenter().Text("رقم الطلب").FontSize(9).Bold().DirectionFromRightToLeft();
                            });

                            // بيانات الجدول - مطابق للورقة مع المبالغ الحقيقية
                            foreach (var item in orderItems)
                            {
                                // المبلغ الحقيقي = الكمية × السعر
                                var actualAmount = item.Quantity * item.PricePerMeter;

                                table.Cell().Element(CellStyle).AlignCenter().Text($"{actualAmount:N2}").FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.PricePerMeter:N2}").FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text("متر").FontSize(9).DirectionFromRightToLeft();
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.FabricType).FontSize(9).DirectionFromRightToLeft();
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.Color).FontSize(9).DirectionFromRightToLeft();
                                table.Cell().Element(CellStyle).AlignCenter().Text($"{item.Quantity:N2}").FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.OrderDate.ToString("dd/MM/yyyy")).FontSize(9);
                                table.Cell().Element(CellStyle).AlignCenter().Text(item.OrderNumber).FontSize(9);
                            }
                        });

                        column.Item().PaddingVertical(10);

                        // الملخص المالي - حسابات حقيقية
                        // إجمالي قيمة الطلبات الفعلية (الكمية × السعر)
                        var invoiceAmount = orderItems.Sum(x => x.Quantity * x.PricePerMeter);

                        decimal previousBalance = 0;
                        decimal currentBalance = 0;
                        decimal totalPaidToCustomer = 0;

                        if (_invoice.CustomerId.HasValue)
                        {
                            var customer = _context.Customers.FirstOrDefault(c => c.CustomerId == _invoice.CustomerId.Value);
                            if (customer != null)
                            {
                                // إجمالي المدفوعات من العميل لهذه الفاتورة فقط
                                // نحسب المدفوعات المرتبطة بالطلبات في هذه الفاتورة
                                var invoiceOrderIds = _context.InvoiceCustomerOrders
                                    .Where(ico => ico.InvoiceId == _invoice.InvoiceId)
                                    .Select(ico => ico.OrderId)
                                    .ToList();

                                // المدفوع من الطلبات المرتبطة بهذه الفاتورة
                                var paidFromInvoiceOrders = _context.CustomerOrders
                                    .Where(o => invoiceOrderIds.Contains(o.OrderId))
                                    .Sum(o => (decimal?)o.PaidAmount) ?? 0;

                                // المدفوعات المباشرة للطلبات في هذه الفاتورة
                                var directPayments = _context.CustomerPayments
                                    .Where(p => p.CustomerId == customer.CustomerId &&
                                               invoiceOrderIds.Contains(p.OrderId ?? 0))
                                    .Sum(p => (decimal?)p.Amount) ?? 0;

                                totalPaidToCustomer = paidFromInvoiceOrders + directPayments;

                                // الرصيد الحالي للعميل (من قاعدة البيانات)
                                // هذا هو إجمالي ما عليه من مديونية
                                currentBalance = customer.Balance;

                                // الرصيد السابق = الرصيد الحالي - إجمالي هذه الفاتورة
                                // (لأن الرصيد الحالي يشمل هذه الفاتورة)
                                previousBalance = currentBalance - invoiceAmount;
                            }
                        }

                        // عنوان رصيد العميل
                        column.Item().PaddingTop(20).AlignCenter().Text("رصيد العميل")
                            .FontSize(16).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        // ملخص الحساب - يمتد على مستوى الصفحة
                        column.Item().PaddingTop(10).BorderColor(QuestColors.Black).Border(1).Table(summaryTable =>
                        {
                            summaryTable.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(2); // القيم
                                columns.RelativeColumn(3); // البيانات
                            });

                            // رصيد السابق (حقيقي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{previousBalance:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("رصيد السابق")
                                .FontSize(10).DirectionFromRightToLeft();

                            // الإجمالي (ثمن البضاعة الفعلي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{invoiceAmount:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("الإجمالي")
                                .FontSize(10).DirectionFromRightToLeft();

                            // مدفوع من العميل (حقيقي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{totalPaidToCustomer:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("مدفوع من العميل")
                                .FontSize(10).DirectionFromRightToLeft();

                            // الرصيد الحالي (حقيقي)
                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{currentBalance:N2}")
                                .FontSize(10).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("الرصيد الحالي")
                                .FontSize(10).DirectionFromRightToLeft();
                        });

                        // النص في أسفل الصفحة - مطابق للورقة
                        column.Item().PaddingTop(20).AlignCenter().Text("سياسة الشركة: لا يتم استرداد البضاعة بعد مرور أكثر من أسبوعين من تاريخ الشراء")
                            .FontSize(9).Bold().DirectionFromRightToLeft();

                        column.Item().PaddingTop(10).AlignCenter().Text("مدفوعة")
                            .FontSize(12).Bold().DirectionFromRightToLeft();

                        // معلومات الطباعة
                        column.Item().PaddingTop(20).Row(footerRow =>
                        {
                            footerRow.RelativeItem().AlignLeft().Text("المستخدم")
                                .FontSize(10).DirectionFromRightToLeft();
                            footerRow.RelativeItem().AlignCenter().Text("الختم")
                                .FontSize(10).DirectionFromRightToLeft();
                            footerRow.RelativeItem().AlignRight().Text("التوقيع")
                                .FontSize(10).DirectionFromRightToLeft();
                        });

                        column.Item().PaddingTop(10).Row(dateRow =>
                        {
                            dateRow.RelativeItem().AlignLeft().Text($"{EgyptTimeHelper.Now:HH:mm}")
                                .FontSize(10);
                            dateRow.RelativeItem().AlignCenter().Text($"{EgyptTimeHelper.Now:dd-MMM-yy}")
                                .FontSize(10);
                            dateRow.RelativeItem().AlignRight().Text("Page 1 of 1")
                                .FontSize(10);
                        });
                    });
                });
            });

            // حفظ المستند
            document.GeneratePdf(fileName);
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في إنشاء PDF: {ex.Message}");
        }
    }

    // دالة مساعدة لتنسيق خلايا الجدول
    private static IContainer CellStyle(IContainer container)
    {
        return container
            .Border(1)
            .BorderColor(QuestColors.Black)
            .Background(QuestColors.White)
            .Padding(5);
    }







    private List<InvoiceOrderViewModel> GetOrderItemsFromDb()
    {
        return _context.InvoiceCustomerOrders
            .Include(x => x.Order)
            .Where(x => x.InvoiceId == _invoice.InvoiceId)
            .Select(x => new InvoiceOrderViewModel
            {
                OrderId = x.Order.OrderId,
                OrderNumber = x.Order.OrderNumber,
                OrderDate = x.Order.OrderDate,
                FabricType = x.Order.FabricType,
                Color = x.Order.Color ?? "",
                Quantity = x.Order.Quantity,
                PricePerMeter = x.Order.PricePerMeter,
                TotalAmount = x.Order.TotalAmount,
                PaidAmount = x.Order.PaidAmount,
                RemainingAmount = x.Order.RemainingAmount,
                StatusText = x.Order.Status.ToString()
            })
            .OrderBy(x => x.OrderDate)
            .ToList();
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        _context?.Dispose();
        base.OnClosed(e);
    }


}
