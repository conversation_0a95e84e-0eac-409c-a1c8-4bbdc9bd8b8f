using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace FactoryManagementSystem.Views
{
    public partial class SelectOrdersForInvoiceWindow : Window
    {
        private readonly FactoryDbContext _context;
        private readonly Customer _selectedCustomer;
        public ObservableCollection<OrderSelectionItem> Orders { get; set; }

        public SelectOrdersForInvoiceWindow(Customer customer)
        {
            InitializeComponent();
            _context = new FactoryDbContext();
            _selectedCustomer = customer;
            Orders = new ObservableCollection<OrderSelectionItem>();
            Orders.CollectionChanged += Orders_CollectionChanged;
            DataContext = this;
            LoadOrders();
        }

        private void Orders_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            UpdateTotals();
        }

        private void LoadOrders()
        {
            try
            {
                // جلب الطلبات غير المفوترة للعميل المحدد
                var unpaidOrders = _context.CustomerOrders
                    .Where(o => o.CustomerId == _selectedCustomer.CustomerId && 
                               o.RemainingAmount > 0 &&
                               !_context.InvoiceCustomerOrders.Any(ico => ico.OrderId == o.OrderId))
                    .OrderByDescending(o => o.OrderDate)
                    .ToList();

                Orders.Clear();
                foreach (var order in unpaidOrders)
                {
                    var orderItem = new OrderSelectionItem
                    {
                        OrderId = order.OrderId,
                        OrderNumber = order.OrderNumber,
                        ProductName = $"{order.FabricType} - {order.Color}",
                        Color = order.Color ?? "",
                        Quantity = order.Quantity,
                        UnitPrice = order.PricePerMeter,
                        TotalPrice = order.RemainingAmount,
                        IsSelected = false
                    };
                    orderItem.PropertyChanged += OrderItem_PropertyChanged;
                    Orders.Add(orderItem);
                }

                UpdateTotals();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلبات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OrderItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(OrderSelectionItem.IsSelected))
            {
                UpdateTotals();
            }
        }

        private void UpdateTotals()
        {
            var selectedOrders = Orders.Where(o => o.IsSelected).ToList();
            var totalQuantity = selectedOrders.Sum(o => o.Quantity);
            var totalAmount = selectedOrders.Sum(o => o.TotalPrice);

            TxtTotalQuantity.Text = totalQuantity.ToString("N2");
            TxtTotalAmount.Text = totalAmount.ToString("N2") + " ج.م";
        }

        private void BtnConfirm_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedOrders = Orders.Where(o => o.IsSelected).ToList();
                if (!selectedOrders.Any())
                {
                    MessageBox.Show("يرجى اختيار طلب واحد على الأقل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء فاتورة جديدة
                var invoice = new Invoice
                {
                    InvoiceNumber = GenerateInvoiceNumber(),
                    InvoiceDate = DateTime.Now,
                    CustomerId = _selectedCustomer.CustomerId,
                    Type = InvoiceType.Sales,
                    Status = InvoiceStatus.Draft,
                    SubTotal = selectedOrders.Sum(o => o.TotalPrice),
                    TotalAmount = selectedOrders.Sum(o => o.TotalPrice),
                    RemainingAmount = selectedOrders.Sum(o => o.TotalPrice)
                };

                _context.Invoices.Add(invoice);
                _context.SaveChanges();

                // ربط الطلبات المختارة بالفاتورة
                foreach (var orderItem in selectedOrders)
                {
                    var invoiceOrder = new InvoiceCustomerOrder
                    {
                        InvoiceId = invoice.InvoiceId,
                        OrderId = orderItem.OrderId,
                        OrderAmount = orderItem.TotalPrice,
                        OrderRemainingAmount = orderItem.TotalPrice
                    };
                    _context.InvoiceCustomerOrders.Add(invoiceOrder);
                }

                _context.SaveChanges();

                MessageBox.Show($"تم إنشاء الفاتورة رقم {invoice.InvoiceNumber} بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء الفاتورة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private string GenerateInvoiceNumber()
        {
            var today = DateTime.Now;
            var prefix = $"INV-{today:yyyyMMdd}-";
            var lastInvoice = _context.Invoices
                .Where(i => i.InvoiceNumber.StartsWith(prefix))
                .OrderByDescending(i => i.InvoiceNumber)
                .FirstOrDefault();

            if (lastInvoice == null)
            {
                return prefix + "001";
            }

            var lastNumber = int.Parse(lastInvoice.InvoiceNumber.Substring(prefix.Length));
            return prefix + (lastNumber + 1).ToString("D3");
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            _context?.Dispose();
            base.OnClosing(e);
        }
    }

    public class OrderSelectionItem : INotifyPropertyChanged
    {
        private bool _isSelected;

        public int OrderId { get; set; }
        public string OrderNumber { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(nameof(IsSelected)));
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;
    }
} 