<Window x:Class="FactoryManagementSystem.Views.CustomerPaymentsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FactoryManagementSystem.Views"
        mc:Ignorable="d"
        Title="مدفوعات العميل"
        Height="700" Width="1000"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        MinHeight="600" MinWidth="800"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <ScrollViewer VerticalScrollBarVisibility="Auto" 
                      HorizontalScrollBarVisibility="Disabled"
                      Padding="5">
            <Border Style="{StaticResource Card}" Margin="20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- العنوان ومعلومات العميل -->
                    <StackPanel Grid.Row="0" Margin="0,0,0,20">
                        <TextBlock Text="💰 مدفوعات العميل" Style="{StaticResource HeaderText}" HorizontalAlignment="Center"/>
                        
                        <Grid Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <StackPanel Grid.Column="0">
                                <TextBlock Text="اسم العميل:" Style="{StaticResource BodyText}" FontWeight="Bold"/>
                                <TextBlock x:Name="TxtCustomerName" Text="" Style="{StaticResource BodyText}" Margin="0,5,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="1">
                                <TextBlock Text="رقم الهاتف:" Style="{StaticResource BodyText}" FontWeight="Bold"/>
                                <TextBlock x:Name="TxtCustomerPhone" Text="" Style="{StaticResource BodyText}" Margin="0,5,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="2">
                                <TextBlock Text="الرصيد الحالي:" Style="{StaticResource BodyText}" FontWeight="Bold"/>
                                <TextBlock x:Name="TxtCurrentBalance" Text="" Style="{StaticResource BodyText}" Margin="0,5,0,0"/>
                            </StackPanel>

                            <StackPanel Grid.Column="3">
                                <TextBlock Text="إجمالي المدفوعات:" Style="{StaticResource BodyText}" FontWeight="Bold"/>
                                <TextBlock x:Name="TxtTotalPayments" Text="" Style="{StaticResource BodyText}" Margin="0,5,0,0" Foreground="{StaticResource AccentColor}"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>

                    <!-- أزرار الإجراءات -->
                    <Border Grid.Row="1" Style="{StaticResource Card}" Background="{StaticResource BackgroundColor}" Margin="0,0,0,20">
                        <Grid>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <Button x:Name="BtnAddPayment"
                                        Content="➕ دفعة جديدة"
                                        Style="{StaticResource PrimaryButton}"
                                        Margin="0,0,10,0"
                                        Click="BtnAddPayment_Click"/>

                                <Button x:Name="BtnEditPayment"
                                        Content="✏️ تعديل"
                                        Style="{StaticResource PrimaryButton}"
                                        Margin="0,0,10,0"
                                        IsEnabled="False"
                                        Click="BtnEditPayment_Click"/>

                                <Button x:Name="BtnDeletePayment"
                                        Content="🗑️ حذف"
                                        Background="{StaticResource DangerColor}"
                                        Foreground="White"
                                        BorderThickness="0"
                                        Padding="16,8"
                                        FontWeight="Medium"
                                        Cursor="Hand"
                                        IsEnabled="False"
                                        Click="BtnDeletePayment_Click">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center"
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#DC2626"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Button.Template>
                                </Button>

                              

                                <Button x:Name="BtnExport"
                                        Content="📋 تصدير"
                                        Style="{StaticResource PrimaryButton}"
                                        Margin="0,0,10,0"
                                        Click="BtnExport_Click"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- جدول المدفوعات -->
                    <Border Grid.Row="2" Style="{StaticResource Card}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- عنوان الجدول -->
                            <TextBlock Grid.Row="0" Text="سجل المدفوعات" Style="{StaticResource SubHeaderText}" Margin="0,0,0,15"/>

                            <!-- الجدول -->
                            <DataGrid Grid.Row="1" x:Name="DgPayments"
                                    AutoGenerateColumns="False"
                                    CanUserAddRows="False"
                                    CanUserDeleteRows="False"
                                    IsReadOnly="True"
                                    SelectionMode="Single"
                                    GridLinesVisibility="Horizontal"
                                    HeadersVisibility="Column"
                                    Background="White"
                                    AlternatingRowBackground="#F8F9FA"
                                    SelectionChanged="DgPayments_SelectionChanged">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="رقم الدفعة" Binding="{Binding PaymentNumber}" Width="100"/>
                                    <DataGridTextColumn Header="التاريخ" Binding="{Binding PaymentDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                                    <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="120"/>
                                    <DataGridTextColumn Header="طريقة الدفع" Binding="{Binding PaymentMethod}" Width="120"/>
                                    <DataGridTextColumn Header="رقم المرجع" Binding="{Binding ReferenceNumber}" Width="120"/>
                                    <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="*"/>
                                    <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>

                            <!-- ملخص المدفوعات -->
                            <Border Grid.Row="2" Background="{StaticResource WarningColor}" CornerRadius="6" Margin="0,15,0,0" Padding="15">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBlock Grid.Column="0" Text="عدد المدفوعات:" Style="{StaticResource BodyText}" 
                                             Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                    
                                    <TextBlock Grid.Column="1" x:Name="TxtPaymentCount" Text="0" Style="{StaticResource BodyText}" 
                                             Foreground="White" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                                    
                                    <TextBlock Grid.Column="2" Text="إجمالي المبلغ:" Style="{StaticResource BodyText}" 
                                             Foreground="White" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    
                                    <TextBlock Grid.Column="3" x:Name="TxtTotalAmount" Text="0.00 ج.م" Style="{StaticResource BodyText}" 
                                             Foreground="White" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
                                    
                                    <TextBlock Grid.Column="4" Text="الرصيد المتبقي:" Style="{StaticResource BodyText}" 
                                             Foreground="White" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                    
                                    <TextBlock Grid.Column="5" x:Name="TxtRemainingBalance" Text="0.00 ج.م" Style="{StaticResource BodyText}" 
                                             Foreground="White" FontWeight="Bold" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                        </Grid>
                    </Border>

                    <!-- زر الإغلاق -->
                    <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button x:Name="BtnClose" 
                                Content="❌ إغلاق" 
                                Background="{StaticResource SecondaryColor}"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="20,10"
                                FontWeight="Medium"
                                Cursor="Hand"
                                Click="BtnClose_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="6"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#475569"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
        </ScrollViewer>
    </Grid>
</Window>
