using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Data;

namespace FactoryManagementSystem.Views
{
    public partial class CustomerPaymentsWindow : Window
    {
        private readonly Customer _customer;
        private List<CustomerPayment> _payments;
        private CustomerPayment? _selectedPayment;

        public CustomerPaymentsWindow(Customer customer)
        {
            InitializeComponent();
            _customer = customer;
            _payments = new List<CustomerPayment>();

            LoadCustomerInfo();
            LoadPayments();
        }

        private void LoadCustomerInfo()
        {
            TxtCustomerName.Text = _customer.Name ?? "غير محدد";
            TxtCustomerPhone.Text = _customer.Phone ?? "غير محدد";
            TxtCurrentBalance.Text = $"{_customer.Balance:N2} ج.م";
        }

        private void LoadPayments()
        {
            try
            {
                using var context = new FactoryDbContext();

                // تحميل المدفوعات من قاعدة البيانات
                _payments = context.CustomerPayments
                    .Where(p => p.CustomerId == _customer.CustomerId)
                    .OrderByDescending(p => p.PaymentDate)
                    .ToList();

                DgPayments.ItemsSource = _payments;
                UpdateTotals();

                // إظهار رسالة إذا لم توجد مدفوعات
                if (_payments.Count == 0)
                {
                    MessageBox.Show($"لا توجد مدفوعات للعميل: {_customer.Name}\n\nيمكنك إضافة دفعة جديدة باستخدام زر 'دفعة جديدة'",
                        "لا توجد مدفوعات", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المدفوعات: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                _payments = new List<CustomerPayment>();
                DgPayments.ItemsSource = _payments;
            }
        }

        private void UpdateTotals()
        {
            try
            {
                if (_payments == null)
                {
                    _payments = new List<CustomerPayment>();
                }

                var totalAmount = _payments.Sum(p => p.Amount);

                TxtPaymentCount.Text = _payments.Count.ToString();
                TxtTotalAmount.Text = $"{totalAmount:N2} ج.م";
                TxtTotalPayments.Text = $"{totalAmount:N2} ج.م";

                // حساب الرصيد المتبقي (افتراضي)
                var remainingBalance = _customer.Balance - totalAmount;
                TxtRemainingBalance.Text = $"{remainingBalance:N2} ج.م";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث الإجماليات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void DgPayments_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            _selectedPayment = DgPayments.SelectedItem as CustomerPayment;
            var hasSelection = _selectedPayment != null;
            
            BtnEditPayment.IsEnabled = hasSelection;
            BtnDeletePayment.IsEnabled = hasSelection;
        }

        private void BtnAddPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addPaymentWindow = new AddCustomerPaymentWindow(_customer);
                addPaymentWindow.Owner = this;

                if (addPaymentWindow.ShowDialog() == true && addPaymentWindow.NewPayment != null)
                {
                    // إعادة تحميل المدفوعات
                    LoadPayments();

                    MessageBox.Show(
                        $"تم تسجيل الدفعة بنجاح! ✅\n\n" +
                        $"رقم الدفعة: {addPaymentWindow.NewPayment.PaymentNumber}\n" +
                        $"المبلغ: {addPaymentWindow.NewPayment.Amount:N2} ج.م\n\n" +
                        $"تم تحديث:\n" +
                        $"• رصيد العميل\n" +
                        $"• رصيد الخزينة\n" +
                        $"• سجل المدفوعات",
                        "تم بنجاح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الدفعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditPayment_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedPayment != null)
            {
                try
                {
                    var editPaymentWindow = new AddCustomerPaymentWindow(_customer, _selectedPayment);
                    editPaymentWindow.Owner = this;
                    if (editPaymentWindow.ShowDialog() == true && editPaymentWindow.NewPayment != null)
                    {
                        using var context = new FactoryDbContext();
                        var paymentToUpdate = context.CustomerPayments.Find(_selectedPayment.PaymentId);
                        if (paymentToUpdate != null)
                        {
                            // تحديث الحقول المطلوبة
                            paymentToUpdate.PaymentNumber = editPaymentWindow.NewPayment.PaymentNumber;
                            paymentToUpdate.PaymentDate = editPaymentWindow.NewPayment.PaymentDate;
                            paymentToUpdate.Amount = editPaymentWindow.NewPayment.Amount;
                            paymentToUpdate.PaymentMethod = editPaymentWindow.NewPayment.PaymentMethod;
                            paymentToUpdate.CheckNumber = editPaymentWindow.NewPayment.CheckNumber;
                            paymentToUpdate.BankName = editPaymentWindow.NewPayment.BankName;
                            paymentToUpdate.CheckDate = editPaymentWindow.NewPayment.CheckDate;
                            paymentToUpdate.Notes = editPaymentWindow.NewPayment.Notes;
                            paymentToUpdate.OrderId = editPaymentWindow.NewPayment.OrderId;
                            context.SaveChanges();
                        }
                        // إعادة تحميل المدفوعات
                        LoadPayments();
                        MessageBox.Show("تم تعديل الدفعة بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تعديل الدفعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnDeletePayment_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedPayment != null)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الدفعة رقم: {_selectedPayment.PaymentNumber}؟\n\nالمبلغ: {_selectedPayment.Amount:N2} ج.م\n\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        using var context = new FactoryDbContext();
                        // حذف الحركة المالية المرتبطة
                        var cashTxs = context.CashTransactions.Where(t => t.Description.Contains($"دفعة") && t.Amount == _selectedPayment.Amount && t.TransactionDate == _selectedPayment.PaymentDate).ToList();
                        context.CashTransactions.RemoveRange(cashTxs);

                        // تحديث PaidAmount/RemainingAmount للطلب المرتبط
                        if (_selectedPayment.OrderId != null)
                        {
                            var order = context.CustomerOrders.FirstOrDefault(o => o.OrderId == _selectedPayment.OrderId);
                            if (order != null)
                            {
                                order.PaidAmount -= _selectedPayment.Amount;
                                order.RemainingAmount = order.TotalAmount - order.PaidAmount;
                                if (order.RemainingAmount > 0)
                                    order.Status = OrderStatus.InProgress;
                                else if (order.PaidAmount <= 0)
                                    order.Status = OrderStatus.Pending;
                            }
                        }

                        // تحديث رصيد العميل
                        var customer = context.Customers.FirstOrDefault(c => c.CustomerId == _selectedPayment.CustomerId);
                        if (customer != null)
                        {
                            customer.Balance += _selectedPayment.Amount;
                        }

                        // حذف الدفعة من قاعدة البيانات
                        var paymentToDelete = context.CustomerPayments.FirstOrDefault(p => p.PaymentId == _selectedPayment.PaymentId);
                        if (paymentToDelete != null)
                        {
                            context.CustomerPayments.Remove(paymentToDelete);
                        }
                        context.SaveChanges();

                        // تحديث الواجهة
                        _payments.Remove(_selectedPayment);
                        DgPayments.ItemsSource = null;
                        DgPayments.ItemsSource = _payments;
                        UpdateTotals();

                        MessageBox.Show("تم حذف الدفعة والحركة المالية وتحديث الرصيد بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الدفعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }



        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

      

        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_payments.Count == 0)
                {
                    MessageBox.Show("لا توجد مدفوعات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير مدفوعات العميل",
                    Filter = "ملف Excel (*.xlsx)|*.xlsx|ملف CSV (*.csv)|*.csv",
                    FileName = $"مدفوعات_{_customer.Name}_{DateTime.Now:yyyyMMdd}.xlsx"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    // تصدير البيانات
                    ExportPaymentsToFile(saveDialog.FileName, saveDialog.FilterIndex);
                    
                    MessageBox.Show($"تم تصدير {_payments.Count} دفعة إلى:\n{saveDialog.FileName}",
                        "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportPaymentsToFile(string fileName, int filterIndex)
        {
            try
            {
                if (filterIndex == 1) // Excel
                {
                    ExportToExcel(fileName);
                }
                else // CSV
                {
                    ExportToCsv(fileName);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير الملف: {ex.Message}");
            }
        }

        private void ExportToExcel(string fileName)
        {
            // استخدام مكتبة EPPlus أو ClosedXML للتصدير إلى Excel
            // مؤقتاً نستخدم CSV مع امتداد xlsx
            ExportToCsv(fileName.Replace(".xlsx", ".csv"));
        }

        private void ExportToCsv(string fileName)
        {
            var csv = new System.Text.StringBuilder();
            
            // إضافة معلومات العميل
            csv.AppendLine($"اسم العميل,{_customer.Name}");
            csv.AppendLine($"رقم الهاتف,{_customer.Phone}");
            csv.AppendLine($"الرصيد الحالي,{_customer.Balance:N2}");
            csv.AppendLine($"إجمالي المدفوعات,{_payments.Sum(p => p.Amount):N2}");
            csv.AppendLine();
            
            // إضافة رؤوس الأعمدة
            csv.AppendLine("رقم الدفعة,التاريخ,المبلغ,طريقة الدفع,رقم الشيك,البنك,تاريخ الشيك,الملاحظات");
            
            // إضافة البيانات
            foreach (var payment in _payments)
            {
                csv.AppendLine($"{payment.PaymentNumber}," +
                              $"{payment.PaymentDate:dd/MM/yyyy}," +
                              $"{payment.Amount:N2}," +
                              $"{payment.PaymentMethod}," +
                              $"{payment.CheckNumber ?? ""}," +
                              $"{payment.BankName ?? ""}," +
                              $"{payment.CheckDate?.ToString("dd/MM/yyyy") ?? ""}," +
                              $"\"{payment.Notes ?? ""}\"");
            }
            
            System.IO.File.WriteAllText(fileName, csv.ToString(), System.Text.Encoding.UTF8);
        }


    }


}
