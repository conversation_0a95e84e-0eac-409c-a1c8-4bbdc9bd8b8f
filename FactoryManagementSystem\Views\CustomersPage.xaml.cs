using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;

namespace FactoryManagementSystem.Views;

/// <summary>
/// صفحة إدارة العملاء
/// </summary>
public partial class CustomersPage : Page
{
    private readonly FactoryDbContext _context = new();
    public ObservableCollection<Customer> Customers { get; set; } = new();
    private Customer? _selectedCustomer;

    public CustomersPage()
    {
        try
        {
            InitializeComponent();
            DgCustomers.ItemsSource = Customers;

            LoadCustomers();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء صفحة العملاء: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void LoadCustomers()
    {
        try
        {
            // التأكد من وجود قاعدة البيانات
            await _context.Database.EnsureCreatedAsync();

            Customers.Clear();
            var customers = await _context.Customers
                .OrderBy(c => c.Name)
                .ToListAsync();

            foreach (var customer in customers)
            {
                Customers.Add(customer);
            }

            UpdateStatistics();

            // إذا مفيش عملاء، أضف بيانات تجريبية
            if (customers.Count == 0)
            {
                await AddSampleCustomers();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات العملاء: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            if (TxtTotalCustomers != null)
                TxtTotalCustomers.Text = Customers?.Count.ToString() ?? "0";

            if (TxtActiveCustomers != null)
                TxtActiveCustomers.Text = Customers?.Count(c => c.IsActive).ToString() ?? "0";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        try
        {
            var searchText = TxtSearch?.Text?.ToLower() ?? "";
            var statusFilter = CmbStatusFilter?.SelectedIndex ?? 0;

            var filteredCustomers = Customers.AsEnumerable();

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                filteredCustomers = filteredCustomers.Where(c =>
                    c.Name.ToLower().Contains(searchText) ||
                    (c.Phone != null && c.Phone.Contains(searchText)) ||
                    (c.City != null && c.City.ToLower().Contains(searchText))
                );
            }

            // تطبيق فلتر الحالة
            switch (statusFilter)
            {
                case 1: // نشط
                    filteredCustomers = filteredCustomers.Where(c => c.IsActive);
                    break;
                case 2: // غير نشط
                    filteredCustomers = filteredCustomers.Where(c => !c.IsActive);
                    break;
                default: // الكل
                    break;
            }

            DgCustomers.ItemsSource = filteredCustomers.ToList();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DgCustomers_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        _selectedCustomer = DgCustomers.SelectedItem as Customer;
        BtnEditCustomer.IsEnabled = _selectedCustomer != null;
        BtnDeleteCustomer.IsEnabled = _selectedCustomer != null;
        BtnCustomerOrders.IsEnabled = _selectedCustomer != null;
        BtnCustomerPayments.IsEnabled = _selectedCustomer != null;
        BtnCustomerStatement.IsEnabled = _selectedCustomer != null;

    }

    private void DgCustomers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            EditCustomer(_selectedCustomer);
        }
    }

    private void BtnAddCustomer_Click(object sender, RoutedEventArgs e)
    {
        var addWindow = new AddEditCustomerWindow();
        if (addWindow.ShowDialog() == true)
        {
            LoadCustomers();
        }
    }

    private void BtnEditCustomer_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            EditCustomer(_selectedCustomer);
        }
    }

    private void EditCustomer(Customer customer)
    {
        var editWindow = new AddEditCustomerWindow(customer);
        if (editWindow.ShowDialog() == true)
        {
            LoadCustomers();
        }
    }

    private async void BtnDeleteCustomer_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer == null) return;

        var result = MessageBox.Show(
            $"هل أنت متأكد من حذف العميل '{_selectedCustomer.Name}'؟\n\nملاحظة: سيتم إلغاء تفعيل العميل بدلاً من حذفه نهائياً.",
            "تأكيد الحذف",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                _selectedCustomer.IsActive = false;
                _selectedCustomer.LastModifiedDate = DateTime.Now;

                await _context.SaveChangesAsync();
                LoadCustomers();

                MessageBox.Show("تم إلغاء تفعيل العميل بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnCustomerOrders_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            try
            {
                var ordersWindow = new CustomerOrdersWindow(_selectedCustomer);
                ordersWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الطلبات: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnCustomerPayments_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            try
            {
                var paymentsWindow = new CustomerPaymentsWindow(_selectedCustomer);
                paymentsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة المدفوعات: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnCustomerStatement_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            try
            {
                var statementWindow = new CustomerStatementWindow(_selectedCustomer);
                statementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح كشف الحساب: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }



    private async Task AddSampleCustomers()
    {
        try
        {
            var sampleCustomers = new List<Customer>
            {
                new Customer
                {
                    Name = "أحمد محمد",
                    Phone = "01012345678",
                    Address = "القاهرة، مصر الجديدة",
                    CreditLimit = 10000,
                    Balance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Customer
                {
                    Name = "فاطمة علي",
                    Phone = "01098765432",
                    Address = "الجيزة، المهندسين",
                    CreditLimit = 15000,
                    Balance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                },
                new Customer
                {
                    Name = "محمد حسن",
                    Phone = "01155667788",
                    Address = "الإسكندرية، سيدي جابر",
                    CreditLimit = 8000,
                    Balance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                }
            };

            _context.Customers.AddRange(sampleCustomers);
            await _context.SaveChangesAsync();

            // إعادة تحميل البيانات
            Customers.Clear();
            var customers = await _context.Customers.OrderBy(c => c.Name).ToListAsync();
            foreach (var customer in customers)
            {
                Customers.Add(customer);
            }
            UpdateStatistics();

            MessageBox.Show("تم إضافة بيانات تجريبية للعملاء!", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة البيانات التجريبية: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تحديث بيانات العملاء
    /// </summary>
    private async void BtnRefreshCustomers_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // تعطيل الزر أثناء التحديث
            BtnRefreshCustomers.IsEnabled = false;
            BtnRefreshCustomers.Content = "🔄 جاري التحديث...";

            // إعادة تحميل البيانات
            await Task.Run(() =>
            {
                Dispatcher.Invoke(() =>
                {
                    LoadCustomers();
                });
            });

            // رسالة نجاح
            MessageBox.Show("تم تحديث بيانات العملاء بنجاح! ✅",
                "تحديث البيانات",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}",
                "خطأ",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }
        finally
        {
            // إعادة تفعيل الزر
            BtnRefreshCustomers.IsEnabled = true;
            BtnRefreshCustomers.Content = "🔄 تحديث";
        }
    }

    ~CustomersPage()
    {
        _context?.Dispose();
    }
}
