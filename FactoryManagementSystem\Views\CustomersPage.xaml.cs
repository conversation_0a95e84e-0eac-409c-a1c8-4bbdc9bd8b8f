using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;

namespace FactoryManagementSystem.Views;

/// <summary>
/// صفحة إدارة العملاء
/// </summary>
public partial class CustomersPage : Page
{
    private readonly FactoryDbContext _context = new();
    public ObservableCollection<Customer> Customers { get; set; } = new();
    private Customer? _selectedCustomer;

    public CustomersPage()
    {
        try
        {
            InitializeComponent();

            // التأكد من تهيئة المجموعة
            if (Customers == null)
                Customers = new ObservableCollection<Customer>();

            // ربط DataGrid بالمجموعة
            if (DgCustomers != null)
                DgCustomers.ItemsSource = Customers;

            LoadCustomers();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء صفحة العملاء: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void LoadCustomers()
    {
        try
        {
            // التأكد من وجود قاعدة البيانات
            if (_context?.Database != null)
            {
                await _context.Database.EnsureCreatedAsync();
            }

            if (Customers != null)
            {
                Customers.Clear();
            }

            var customers = _context?.Customers != null
                ? await _context.Customers.OrderBy(c => c.Name).ToListAsync()
                : new List<Customer>();

            if (customers != null && Customers != null)
            {
                foreach (var customer in customers)
                {
                    if (customer != null)
                    {
                        Customers.Add(customer);
                    }
                }
            }

            UpdateStatistics();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات العملاء: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            if (TxtTotalCustomers != null)
                TxtTotalCustomers.Text = Customers?.Count.ToString() ?? "0";

            if (TxtActiveCustomers != null)
                TxtActiveCustomers.Text = Customers?.Count(c => c.IsActive).ToString() ?? "0";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        try
        {
            if (Customers == null || DgCustomers == null)
                return;

            var searchText = TxtSearch?.Text?.ToLower() ?? "";
            var statusFilter = CmbStatusFilter?.SelectedIndex ?? 0;

            var filteredCustomers = Customers.AsEnumerable();

            // تطبيق فلتر البحث
            if (!string.IsNullOrWhiteSpace(searchText))
            {
                filteredCustomers = filteredCustomers.Where(c => c != null &&
                    (c.Name?.ToLower().Contains(searchText) == true ||
                    (c.Phone?.Contains(searchText) == true) ||
                    (c.City?.ToLower().Contains(searchText) == true))
                );
            }

            // تطبيق فلتر الحالة
            switch (statusFilter)
            {
                case 1: // نشط
                    filteredCustomers = filteredCustomers.Where(c => c?.IsActive == true);
                    break;
                case 2: // غير نشط
                    filteredCustomers = filteredCustomers.Where(c => c?.IsActive == false);
                    break;
                default: // الكل
                    break;
            }

            DgCustomers.ItemsSource = filteredCustomers.ToList();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DgCustomers_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        _selectedCustomer = DgCustomers.SelectedItem as Customer;
        BtnEditCustomer.IsEnabled = _selectedCustomer != null;
        BtnDeleteCustomer.IsEnabled = _selectedCustomer != null;
        BtnCustomerOrders.IsEnabled = _selectedCustomer != null;
        BtnCustomerPayments.IsEnabled = _selectedCustomer != null;
        BtnCustomerStatement.IsEnabled = _selectedCustomer != null;

    }

    private void DgCustomers_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            EditCustomer(_selectedCustomer);
        }
    }

    private void BtnAddCustomer_Click(object sender, RoutedEventArgs e)
    {
        var addWindow = new AddEditCustomerWindow();
        if (addWindow.ShowDialog() == true)
        {
            LoadCustomers();
        }
    }

    private void BtnEditCustomer_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            EditCustomer(_selectedCustomer);
        }
    }

    private void EditCustomer(Customer customer)
    {
        var editWindow = new AddEditCustomerWindow(customer);
        if (editWindow.ShowDialog() == true)
        {
            LoadCustomers();
        }
    }

    private async void BtnDeleteCustomer_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer == null) return;

        var result = MessageBox.Show(
            $"هل أنت متأكد من حذف العميل '{_selectedCustomer.Name}'؟\n\nملاحظة: سيتم إلغاء تفعيل العميل بدلاً من حذفه نهائياً.",
            "تأكيد الحذف",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            try
            {
                _selectedCustomer.IsActive = false;
                _selectedCustomer.LastModifiedDate = DateTime.Now;

                await _context.SaveChangesAsync();
                LoadCustomers();

                MessageBox.Show("تم إلغاء تفعيل العميل بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnCustomerOrders_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            try
            {
                var ordersWindow = new CustomerOrdersWindow(_selectedCustomer);
                ordersWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة الطلبات: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnCustomerPayments_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            try
            {
                var paymentsWindow = new CustomerPaymentsWindow(_selectedCustomer);
                paymentsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة المدفوعات: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnCustomerStatement_Click(object sender, RoutedEventArgs e)
    {
        if (_selectedCustomer != null)
        {
            try
            {
                var statementWindow = new CustomerStatementWindow(_selectedCustomer);
                statementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح كشف الحساب: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }



    private void BtnAddSampleData_Click(object sender, RoutedEventArgs e)
    {
        AddSampleCustomers();
    }

    private async void AddSampleCustomers()
    {
        try
        {
            // التحقق من وجود بيانات
            var existingCount = await _context.Customers.CountAsync();
            if (existingCount > 0)
            {
                MessageBox.Show("توجد بيانات عملاء بالفعل!", "معلومات",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var sampleCustomers = new List<Customer>
            {
                new Customer
                {
                    Name = "أحمد محمد علي",
                    Phone = "01012345678",
                    Address = "شارع النيل، القاهرة الجديدة",
                    City = "القاهرة",
                    Email = "<EMAIL>",
                    CreditLimit = 15000,
                    Balance = 2500,
                    IsActive = true,
                    Notes = "عميل مميز - دفع منتظم",
                    CreatedDate = DateTime.Now
                },
                new Customer
                {
                    Name = "فاطمة علي حسن",
                    Phone = "01098765432",
                    Address = "شارع الهرم، الجيزة",
                    City = "الجيزة",
                    Email = "<EMAIL>",
                    CreditLimit = 20000,
                    Balance = 0,
                    IsActive = true,
                    Notes = "عميل جديد - حد ائتماني عالي",
                    CreatedDate = DateTime.Now
                },
                new Customer
                {
                    Name = "محمد حسن أحمد",
                    Phone = "01155667788",
                    Address = "كورنيش الإسكندرية، سيدي جابر",
                    City = "الإسكندرية",
                    Email = "<EMAIL>",
                    CreditLimit = 12000,
                    Balance = 1800,
                    IsActive = true,
                    Notes = "عميل قديم - علاقة تجارية ممتازة",
                    CreatedDate = DateTime.Now
                },
                new Customer
                {
                    Name = "سارة محمود",
                    Phone = "01066778899",
                    Address = "شارع الجمهورية، طنطا",
                    City = "طنطا",
                    Email = "<EMAIL>",
                    CreditLimit = 8000,
                    Balance = 500,
                    IsActive = true,
                    Notes = "عميل متوسط الحجم",
                    CreatedDate = DateTime.Now
                },
                new Customer
                {
                    Name = "خالد عبد الرحمن",
                    Phone = "01177889900",
                    Address = "شارع السلام، المنصورة",
                    City = "المنصورة",
                    Email = "<EMAIL>",
                    CreditLimit = 25000,
                    Balance = 0,
                    IsActive = false,
                    Notes = "عميل غير نشط مؤقتاً",
                    CreatedDate = DateTime.Now
                }
            };

            _context.Customers.AddRange(sampleCustomers);
            await _context.SaveChangesAsync();

            // إعادة تحميل البيانات
            LoadCustomers();

            MessageBox.Show("تم إضافة بيانات تجريبية للعملاء بنجاح!", "معلومات",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة البيانات التجريبية: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تحديث بيانات العملاء
    /// </summary>
    private async void BtnRefreshCustomers_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // تعطيل الزر أثناء التحديث
            BtnRefreshCustomers.IsEnabled = false;
            BtnRefreshCustomers.Content = "🔄 جاري التحديث...";

            // إعادة تحميل البيانات
            await Task.Run(() =>
            {
                Dispatcher.Invoke(() =>
                {
                    LoadCustomers();
                });
            });

            // رسالة نجاح
            MessageBox.Show("تم تحديث بيانات العملاء بنجاح! ✅",
                "تحديث البيانات",
                MessageBoxButton.OK,
                MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}",
                "خطأ",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }
        finally
        {
            // إعادة تفعيل الزر
            BtnRefreshCustomers.IsEnabled = true;
            BtnRefreshCustomers.Content = "🔄 تحديث";
        }
    }

    ~CustomersPage()
    {
        _context?.Dispose();
    }
}
