﻿#pragma checksum "..\..\..\..\..\Views\ChecksPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "368130B0B40382FECF921619A1DCAA4819F6AD5F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FactoryManagementSystem.Helpers;
using FactoryManagementSystem.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FactoryManagementSystem.Views {
    
    
    /// <summary>
    /// ChecksPage
    /// </summary>
    public partial class ChecksPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 57 "..\..\..\..\..\Views\ChecksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\..\Views\ChecksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatusFilter;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\..\Views\ChecksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbTypeFilter;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\ChecksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgChecks;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\..\..\Views\ChecksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtPendingTotal;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\..\..\Views\ChecksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCashedTotal;
        
        #line default
        #line hidden
        
        
        #line 454 "..\..\..\..\..\Views\ChecksPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtOutgoingTotal;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FactoryManagementSystem;V3.0.0.0;component/views/checkspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\ChecksPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 58 "..\..\..\..\..\Views\ChecksPage.xaml"
            this.TxtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CmbStatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 61 "..\..\..\..\..\Views\ChecksPage.xaml"
            this.CmbStatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbStatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CmbTypeFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 71 "..\..\..\..\..\Views\ChecksPage.xaml"
            this.CmbTypeFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbTypeFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 77 "..\..\..\..\..\Views\ChecksPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 78 "..\..\..\..\..\Views\ChecksPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 79 "..\..\..\..\..\Views\ChecksPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnComprehensiveReport_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.DgChecks = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            this.TxtPendingTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtCashedTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtOutgoingTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 8:
            
            #line 316 "..\..\..\..\..\Views\ChecksPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnCashCheck_Click);
            
            #line default
            #line hidden
            break;
            case 9:
            
            #line 338 "..\..\..\..\..\Views\ChecksPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnPayToSupplier_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 360 "..\..\..\..\..\Views\ChecksPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnReturnCheck_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 384 "..\..\..\..\..\Views\ChecksPage.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnViewParty_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

