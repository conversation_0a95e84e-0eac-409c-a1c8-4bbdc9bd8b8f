using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;
using System.IO;
using Microsoft.Win32;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using System.Diagnostics;
using QuestColors = QuestPDF.Helpers.Colors;

namespace FactoryManagementSystem.Views;

public partial class CustomerStatementWindow : Window
{
    private readonly Customer _customer;
    private readonly FactoryDbContext _context;
    public ObservableCollection<CustomerOrderViewModel> Orders { get; set; }
    public ObservableCollection<CustomerPaymentViewModel> Payments { get; set; }

    public CustomerStatementWindow(Customer customer)
    {
        InitializeComponent();
        _customer = customer ?? throw new ArgumentNullException(nameof(customer));
        _context = new FactoryDbContext();
        
        Orders = new ObservableCollection<CustomerOrderViewModel>();
        Payments = new ObservableCollection<CustomerPaymentViewModel>();
        
        DgOrders.ItemsSource = Orders;
        DgPayments.ItemsSource = Payments;

        // تعيين التواريخ الافتراضية (آخر 3 شهور) بالتوقيت المصري
        DpToDate.SelectedDate = EgyptTimeHelper.Today;
        DpFromDate.SelectedDate = EgyptTimeHelper.AddMonths(-3);

        LoadData();
    }

    private async void LoadData()
    {
        var fromDate = DpFromDate.SelectedDate ?? EgyptTimeHelper.AddMonths(-3);
        var toDate = DpToDate.SelectedDate ?? EgyptTimeHelper.Today;
        var transactionType = ((ComboBoxItem)CmbTransactionType.SelectedItem)?.Tag?.ToString() ?? "All";

        try
        {
            // تحميل بيانات العميل
            TxtCustomerName.Text = _customer.Name;
            TxtCustomerPhone.Text = _customer.Phone ?? "غير محدد";
            TxtCreditLimit.Text = $"{_customer.CreditLimit:N2} ج.م";
            TxtCurrentBalance.Text = $"{_customer.Balance:N2} ج.م";

            // تحميل الطلبات (مع الفلترة)
            Orders.Clear();
            if (transactionType == "All" || transactionType == "Orders")
            {
                var orders = await _context.CustomerOrders
                    .Where(o => o.CustomerId == _customer.CustomerId &&
                               o.OrderDate >= fromDate &&
                               o.OrderDate <= toDate)
                    .OrderByDescending(o => o.OrderDate)
                    .ToListAsync();

            foreach (var order in orders)
            {
                Orders.Add(new CustomerOrderViewModel
                {
                    OrderNumber = order.OrderNumber,
                    OrderDate = order.OrderDate,
                    FabricType = order.FabricType,
                    Color = order.Color ?? "",
                    Quantity = order.Quantity,
                    PricePerMeter = order.PricePerMeter,
                    TotalAmount = order.TotalAmount,
                    PaidAmount = order.PaidAmount,
                    RemainingAmount = order.RemainingAmount,
                    StatusText = GetOrderStatusText(order.Status)
                });
            }
            }

            // تحميل المدفوعات (مع الفلترة)
            Payments.Clear();
            if (transactionType == "All" || transactionType == "Payments")
            {
                var payments = await _context.CustomerPayments
                    .Where(p => p.CustomerId == _customer.CustomerId &&
                               p.PaymentDate >= fromDate &&
                               p.PaymentDate <= toDate)
                    .OrderByDescending(p => p.PaymentDate)
                    .ToListAsync();

                foreach (var payment in payments)
            {
                Payments.Add(new CustomerPaymentViewModel
                {
                    PaymentNumber = payment.PaymentNumber,
                    PaymentDate = payment.PaymentDate,
                    Amount = payment.Amount,
                    PaymentMethodText = GetPaymentMethodText(payment.PaymentMethod),
                    CheckNumber = payment.CheckNumber ?? "",
                    BankName = payment.BankName ?? "",
                    Notes = payment.Notes ?? ""
                });
            }
            }

            // حساب الملخص
            var totalOrders = Orders.Sum(o => o.TotalAmount);
            var totalPayments = Payments.Sum(p => p.Amount);
            var remainingAmount = Orders.Sum(o => o.RemainingAmount);

            TxtTotalOrders.Text = $"{totalOrders:N2} ج.م";
            TxtTotalPayments.Text = $"{totalPayments:N2} ج.م";
            TxtRemainingAmount.Text = $"{remainingAmount:N2} ج.م";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnFilter_Click(object sender, RoutedEventArgs e)
    {
        LoadData();
    }



    private string GetOrderStatusText(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "معلق",
            OrderStatus.InProgress => "قيد التنفيذ",
            OrderStatus.PartiallyPaid => "مدفوع جزئياً",
            OrderStatus.Completed => "مكتمل",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "ملغي",
            _ => "غير محدد"
        };
    }

    private string GetPaymentMethodText(PaymentMethod method)
    {
        return method switch
        {
            PaymentMethod.Cash => "نقدي",
            PaymentMethod.Check => "شيك",
            PaymentMethod.BankTransfer => "تحويل بنكي",
            PaymentMethod.CreditCard => "بطاقة ائتمان",
            PaymentMethod.VodafoneCash => "فودافون كاش",
            PaymentMethod.InstaPay => "انستا باي",
            PaymentMethod.Orange => "أورانج موني",
            PaymentMethod.Etisalat => "اتصالات كاش",
            _ => "غير محدد"
        };
    }

    private void BtnPrint_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "PDF Files (*.pdf)|*.pdf",
                FileName = $"كشف_حساب_العميل_{_customer.Name}_{DateTime.Now:yyyyMMdd}.pdf",
                Title = "حفظ كشف حساب العميل"
            };

            if (saveDialog.ShowDialog() == true)
            {
                GenerateCustomerStatementPdf(saveDialog.FileName);

                var result = MessageBox.Show(
                    $"تم حفظ كشف الحساب بنجاح!\n\nالمسار: {saveDialog.FileName}\n\nهل تريد فتح الملف الآن؟",
                    "تم الحفظ بنجاح",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    OpenPdfFile(saveDialog.FileName);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء كشف الحساب: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnExport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_customer == null)
            {
                MessageBox.Show("بيانات العميل غير متاحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            var fileName = Helpers.ExcelExportHelper.ShowSaveDialog(
                $"كشف_حساب_{_customer.Name}_{DateTime.Now:yyyyMMdd}.csv",
                "تصدير كشف حساب العميل");

            if (!string.IsNullOrEmpty(fileName))
            {
                // إنشاء قائمة بسيطة من النصوص للتقرير
                var reportLines = new List<string>();
                var report = GenerateDetailedReport();
                reportLines.AddRange(report.Split('\n').Where(line => !string.IsNullOrWhiteSpace(line)));

                Helpers.ExcelExportHelper.ExportSimpleList(
                    reportLines,
                    fileName,
                    $"كشف حساب العميل - {_customer.Name}",
                    "تفاصيل الحساب");

                MessageBox.Show($"تم تصدير كشف الحساب بنجاح إلى:\n{fileName}", "تصدير ناجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private string GenerateDetailedReport()
    {
        var report = $@"
===========================================
                كشف حساب العميل
===========================================

📋 بيانات العميل:
الاسم: {_customer.Name}
الهاتف: {_customer.Phone ?? "غير محدد"}
العنوان: {_customer.Address ?? "غير محدد"}
حد الائتمان: {_customer.CreditLimit:N2} ج.م
الرصيد الحالي: {_customer.Balance:N2} ج.م

===========================================
📊 ملخص الحساب:
===========================================
إجمالي الطلبات: {TxtTotalOrders.Text}
إجمالي المدفوعات: {TxtTotalPayments.Text}
المبلغ المتبقي: {TxtRemainingAmount.Text}

===========================================
📋 تفاصيل الطلبات:
===========================================";

        foreach (var order in Orders)
        {
            report += $@"
• {order.OrderNumber} - {order.OrderDate:yyyy/MM/dd}
  {order.FabricType} - {order.Color}
  الكمية: {order.Quantity:N2} متر × {order.PricePerMeter:N2} ج.م
  الإجمالي: {order.TotalAmount:N2} ج.م
  المدفوع: {order.PaidAmount:N2} ج.م
  المتبقي: {order.RemainingAmount:N2} ج.م
  الحالة: {order.StatusText}
";
        }

        report += $@"

===========================================
💰 تفاصيل المدفوعات:
===========================================";

        foreach (var payment in Payments)
        {
            report += $@"
• {payment.PaymentNumber} - {payment.PaymentDate:yyyy/MM/dd}
  المبلغ: {payment.Amount:N2} ج.م
  الطريقة: {payment.PaymentMethodText}
  المرجع: {payment.CheckNumber}
  التفاصيل: {payment.BankName}
  ملاحظات: {payment.Notes}
";
        }

        report += $@"

===========================================
تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}
===========================================";

        return report;
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    private void GenerateCustomerStatementPdf(string fileName)
    {
        try
        {
            // تهيئة QuestPDF
            QuestPDF.Settings.License = LicenseType.Community;

            // جمع البيانات للفترة المحددة
            var fromDate = (DpFromDate.SelectedDate ?? DateTime.Now.AddYears(-1)).Date;
            var toDate = (DpToDate.SelectedDate ?? DateTime.Now).Date.AddDays(1).AddTicks(-1);

            var orders = _context.CustomerOrders
                .Where(o => o.CustomerId == _customer.CustomerId &&
                           o.OrderDate >= fromDate &&
                           o.OrderDate <= toDate)
                .OrderBy(o => o.OrderDate)
                .ToList();

            var payments = _context.CustomerPayments
                .Where(p => p.CustomerId == _customer.CustomerId &&
                           p.PaymentDate >= fromDate &&
                           p.PaymentDate <= toDate)
                .OrderBy(p => p.PaymentDate)
                .ToList();

            // إنشاء مستند PDF
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Size(PageSizes.A4);
                    page.Margin(2, Unit.Centimetre);
                    page.PageColor(QuestColors.White);
                    page.DefaultTextStyle(x => x.FontSize(10).FontFamily("Tahoma").Bold().DirectionFromRightToLeft());

                    page.Content().Column(column =>
                    {
                        // رأس التقرير
                        column.Item().AlignCenter().Text("مصنع مستر شيكو")
                            .FontSize(18).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        column.Item().AlignCenter().Text("كشف حساب العميل")
                            .FontSize(16).Bold().FontColor(QuestColors.Black).DirectionFromRightToLeft();

                        column.Item().PaddingVertical(10);

                        // معلومات العميل والفترة
                        column.Item().Table(headerTable =>
                        {
                            headerTable.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(1);
                                columns.RelativeColumn(1);
                            });

                            // معلومات العميل
                            headerTable.Cell().Element(CellStyle).Padding(5).Column(customerColumn =>
                            {
                                customerColumn.Item().Text($"اسم العميل: {_customer.Name}")
                                    .FontSize(12).Bold().DirectionFromRightToLeft();
                                customerColumn.Item().Text($"الهاتف: {_customer.Phone ?? "غير محدد"}")
                                    .FontSize(10).DirectionFromRightToLeft();
                                customerColumn.Item().Text($"العنوان: {_customer.Address ?? "غير محدد"}")
                                    .FontSize(10).DirectionFromRightToLeft();
                            });

                            // معلومات الفترة
                            headerTable.Cell().Element(CellStyle).Padding(5).Column(periodColumn =>
                            {
                                periodColumn.Item().Text($"من تاريخ: {fromDate:dd/MM/yyyy}")
                                    .FontSize(10).Bold().DirectionFromRightToLeft();
                                periodColumn.Item().Text($"إلى تاريخ: {toDate:dd/MM/yyyy}")
                                    .FontSize(10).DirectionFromRightToLeft();
                                periodColumn.Item().Text($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}")
                                    .FontSize(10).DirectionFromRightToLeft();
                            });
                        });

                        column.Item().PaddingVertical(10);

                        // جدول الطلبات
                        if (orders.Any())
                        {
                            column.Item().Text("الطلبات:")
                                .FontSize(14).Bold().DirectionFromRightToLeft();

                            column.Item().Table(ordersTable =>
                            {
                                ordersTable.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(2); // المبلغ الإجمالي
                                    columns.RelativeColumn(2); // المبلغ المدفوع
                                    columns.RelativeColumn(2); // المبلغ المتبقي
                                    columns.RelativeColumn(1.5f); // الكمية
                                    columns.RelativeColumn(2); // السعر
                                    columns.RelativeColumn(2); // نوع القماش
                                    columns.RelativeColumn(1.5f); // اللون
                                    columns.RelativeColumn(2); // التاريخ
                                    columns.RelativeColumn(2); // رقم الطلب
                                });

                                // رأس الجدول
                                ordersTable.Cell().Element(HeaderCellStyle).Text("المبلغ الإجمالي").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("المبلغ المدفوع").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("المبلغ المتبقي").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("الكمية").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("السعر").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("نوع القماش").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("اللون").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("التاريخ").FontSize(9).DirectionFromRightToLeft();
                                ordersTable.Cell().Element(HeaderCellStyle).Text("رقم الطلب").FontSize(9).DirectionFromRightToLeft();

                                // بيانات الطلبات
                                foreach (var order in orders)
                                {
                                    ordersTable.Cell().Element(CellStyle).Text($"{order.TotalAmount:N2}").FontSize(8).AlignCenter();
                                    ordersTable.Cell().Element(CellStyle).Text($"{order.PaidAmount:N2}").FontSize(8).AlignCenter();
                                    ordersTable.Cell().Element(CellStyle).Text($"{order.RemainingAmount:N2}").FontSize(8).AlignCenter();
                                    ordersTable.Cell().Element(CellStyle).Text($"{order.Quantity:N1}").FontSize(8).AlignCenter();
                                    ordersTable.Cell().Element(CellStyle).Text($"{order.PricePerMeter:N2}").FontSize(8).AlignCenter();
                                    ordersTable.Cell().Element(CellStyle).Text(order.FabricType ?? "").FontSize(8).DirectionFromRightToLeft();
                                    ordersTable.Cell().Element(CellStyle).Text(order.Color ?? "").FontSize(8).DirectionFromRightToLeft();
                                    ordersTable.Cell().Element(CellStyle).Text(order.OrderDate.ToString("dd/MM/yyyy")).FontSize(8).AlignCenter();
                                    ordersTable.Cell().Element(CellStyle).Text(order.OrderNumber).FontSize(8).AlignCenter();
                                }
                            });

                            column.Item().PaddingVertical(10);
                        }

                        // جدول المدفوعات
                        if (payments.Any())
                        {
                            column.Item().Text("المدفوعات:")
                                .FontSize(14).Bold().DirectionFromRightToLeft();

                            column.Item().Table(paymentsTable =>
                            {
                                paymentsTable.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(3); // ملاحظات
                                    columns.RelativeColumn(2); // البنك
                                    columns.RelativeColumn(2); // رقم الشيك
                                    columns.RelativeColumn(2); // طريقة الدفع
                                    columns.RelativeColumn(2); // المبلغ
                                    columns.RelativeColumn(2); // التاريخ
                                    columns.RelativeColumn(2); // رقم الدفعة
                                });

                                // رأس الجدول
                                paymentsTable.Cell().Element(HeaderCellStyle).Text("ملاحظات").FontSize(9).DirectionFromRightToLeft();
                                paymentsTable.Cell().Element(HeaderCellStyle).Text("البنك").FontSize(9).DirectionFromRightToLeft();
                                paymentsTable.Cell().Element(HeaderCellStyle).Text("رقم الشيك").FontSize(9).DirectionFromRightToLeft();
                                paymentsTable.Cell().Element(HeaderCellStyle).Text("طريقة الدفع").FontSize(9).DirectionFromRightToLeft();
                                paymentsTable.Cell().Element(HeaderCellStyle).Text("المبلغ").FontSize(9).DirectionFromRightToLeft();
                                paymentsTable.Cell().Element(HeaderCellStyle).Text("التاريخ").FontSize(9).DirectionFromRightToLeft();
                                paymentsTable.Cell().Element(HeaderCellStyle).Text("رقم الدفعة").FontSize(9).DirectionFromRightToLeft();

                                // بيانات المدفوعات
                                foreach (var payment in payments)
                                {
                                    paymentsTable.Cell().Element(CellStyle).Text(payment.Notes ?? "").FontSize(8).DirectionFromRightToLeft();
                                    paymentsTable.Cell().Element(CellStyle).Text(payment.BankName ?? "").FontSize(8).DirectionFromRightToLeft();
                                    paymentsTable.Cell().Element(CellStyle).Text(payment.CheckNumber ?? "").FontSize(8).AlignCenter();
                                    paymentsTable.Cell().Element(CellStyle).Text(GetPaymentMethodText(payment.PaymentMethod)).FontSize(8).DirectionFromRightToLeft();
                                    paymentsTable.Cell().Element(CellStyle).Text($"{payment.Amount:N2}").FontSize(8).AlignCenter();
                                    paymentsTable.Cell().Element(CellStyle).Text(payment.PaymentDate.ToString("dd/MM/yyyy")).FontSize(8).AlignCenter();
                                    paymentsTable.Cell().Element(CellStyle).Text(payment.PaymentNumber).FontSize(8).AlignCenter();
                                }
                            });

                            column.Item().PaddingVertical(10);
                        }

                        // ملخص الحساب
                        var totalOrders = orders.Sum(o => o.TotalAmount);
                        var totalPaid = payments.Sum(p => p.Amount);
                        var currentBalance = _customer.Balance;

                        column.Item().Table(summaryTable =>
                        {
                            summaryTable.ColumnsDefinition(columns =>
                            {
                                columns.RelativeColumn(2); // القيم
                                columns.RelativeColumn(3); // البيانات
                            });

                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{totalOrders:N2}")
                                .FontSize(12).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("إجمالي الطلبات")
                                .FontSize(12).DirectionFromRightToLeft();

                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{totalPaid:N2}")
                                .FontSize(12).Bold();
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("إجمالي المدفوعات")
                                .FontSize(12).DirectionFromRightToLeft();

                            summaryTable.Cell().Element(CellStyle).AlignCenter()
                                .Text($"{currentBalance:N2}")
                                .FontSize(12).Bold().FontColor(currentBalance >= 0 ? QuestColors.Green.Medium : QuestColors.Red.Medium);
                            summaryTable.Cell().Element(CellStyle).AlignRight().Padding(5)
                                .Text("الرصيد الحالي")
                                .FontSize(12).DirectionFromRightToLeft();
                        });
                    });
                });
            });

            // حفظ الملف
            var pdfBytes = document.GeneratePdf();
            File.WriteAllBytes(fileName, pdfBytes);
        }
        catch (Exception ex)
        {
            throw new Exception($"خطأ في إنشاء ملف PDF: {ex.Message}");
        }
    }

    private static IContainer CellStyle(IContainer container)
    {
        return container
            .Border(1)
            .BorderColor(QuestColors.Grey.Lighten2)
            .Padding(5)
            .AlignMiddle();
    }

    private static IContainer HeaderCellStyle(IContainer container)
    {
        return container
            .Border(1)
            .BorderColor(QuestColors.Black)
            .Background(QuestColors.Grey.Lighten3)
            .Padding(5)
            .AlignMiddle()
            .AlignCenter();
    }

    private void OpenPdfFile(string filePath)
    {
        try
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = filePath,
                UseShellExecute = true
            };
            Process.Start(startInfo);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"تعذر فتح الملف: {ex.Message}\n\nيمكنك العثور على الملف في:\n{filePath}",
                "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        _context?.Dispose();
        base.OnClosed(e);
    }
}

// ViewModels للعرض
public class CustomerOrderViewModel
{
    public string OrderNumber { get; set; } = "";
    public DateTime OrderDate { get; set; }
    public string FabricType { get; set; } = "";
    public string Color { get; set; } = "";
    public decimal Quantity { get; set; }
    public decimal PricePerMeter { get; set; }
    public decimal TotalAmount { get; set; }
    public decimal PaidAmount { get; set; }
    public decimal RemainingAmount { get; set; }
    public string StatusText { get; set; } = "";
}

public class CustomerPaymentViewModel
{
    public string PaymentNumber { get; set; } = "";
    public DateTime PaymentDate { get; set; }
    public decimal Amount { get; set; }
    public string PaymentMethodText { get; set; } = "";
    public string CheckNumber { get; set; } = "";
    public string BankName { get; set; } = "";
    public string Notes { get; set; } = "";
}
