using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using QuestColors = QuestPDF.Helpers.Colors;

namespace FactoryManagementSystem.Helpers
{
    /// <summary>
    /// مساعد لإنشاء تقارير PDF مضغوطة وموحدة التصميم
    /// </summary>
    public static class CompactPdfHelper
    {
        // إعدادات التصميم المضغوط
        public static class CompactSettings
        {
            public const float TableFontSize = 9f;
            public const float HeaderFontSize = 10f;
            public const float TitleFontSize = 16f;
            public const float CompanyNameFontSize = 18f;
            
            public const float CellPaddingVertical = 2f;
            public const float CellPaddingHorizontal = 3f;
            public const float TableSpacing = 5f;
            
            public const float PageMargin = 0.8f; // في السنتيمتر
            public const float SectionSpacing = 8f;
        }

        /// <summary>
        /// إعداد الصفحة الأساسية مع الهوامش المضغوطة
        /// </summary>
        public static void SetupCompactPage(this PageDescriptor page)
        {
            page.Size(PageSizes.A4);
            page.Margin(CompactSettings.PageMargin, Unit.Centimetre);
            page.PageColor(QuestColors.White);
            page.DefaultTextStyle(x => x
                .FontSize(CompactSettings.TableFontSize)
                .FontFamily("Tahoma")
                .DirectionFromRightToLeft());
        }

        /// <summary>
        /// تنسيق خلية الجدول المضغوطة
        /// </summary>
        public static IContainer CompactCellStyle(IContainer container)
        {
            return container
                .Border(0.5f)
                .BorderColor(QuestColors.Grey.Medium)
                .Background(QuestColors.White)
                .PaddingVertical(CompactSettings.CellPaddingVertical)
                .PaddingHorizontal(CompactSettings.CellPaddingHorizontal)
                .AlignMiddle();
        }

        /// <summary>
        /// تنسيق خلية رأس الجدول المضغوطة
        /// </summary>
        public static IContainer CompactHeaderCellStyle(IContainer container)
        {
            return container
                .Border(0.5f)
                .BorderColor(QuestColors.Grey.Darken1)
                .Background(QuestColors.Grey.Lighten4)
                .PaddingVertical(CompactSettings.CellPaddingVertical)
                .PaddingHorizontal(CompactSettings.CellPaddingHorizontal)
                .AlignMiddle();
        }

        /// <summary>
        /// إضافة رأس الشركة المضغوط
        /// </summary>
        public static void AddCompactCompanyHeader(this ColumnDescriptor column, string title)
        {
            column.Item().AlignCenter().Text("مصنع مستر شيكو")
                .FontSize(CompactSettings.CompanyNameFontSize)
                .Bold()
                .FontColor(QuestColors.Black)
                .DirectionFromRightToLeft();

            column.Item().AlignCenter().Text(title)
                .FontSize(CompactSettings.TitleFontSize)
                .Bold()
                .FontColor(QuestColors.Black)
                .DirectionFromRightToLeft();

            column.Item().PaddingVertical(CompactSettings.SectionSpacing);
        }

        /// <summary>
        /// إضافة معلومات العميل/المورد المضغوطة
        /// </summary>
        public static void AddCompactCustomerInfo(this ColumnDescriptor column, string name, string phone, string address, DateTime date, string documentNumber)
        {
            column.Item().Row(row =>
            {
                // العمود الأيمن - معلومات العميل
                row.RelativeItem(1).Column(rightColumn =>
                {
                    rightColumn.Item().Text($"العميل: {name}")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();
                    
                    if (!string.IsNullOrEmpty(phone))
                    {
                        rightColumn.Item().Text($"الهاتف: {phone}")
                            .FontSize(CompactSettings.TableFontSize)
                            .DirectionFromRightToLeft();
                    }
                    
                    if (!string.IsNullOrEmpty(address))
                    {
                        rightColumn.Item().Text($"العنوان: {address}")
                            .FontSize(CompactSettings.TableFontSize)
                            .DirectionFromRightToLeft();
                    }
                });

                // العمود الأيسر - معلومات المستند
                row.RelativeItem(1).Column(leftColumn =>
                {
                    leftColumn.Item().AlignLeft().Text($"رقم المستند: {documentNumber}")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();
                    
                    leftColumn.Item().AlignLeft().Text($"التاريخ: {date:yyyy/MM/dd}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();
                    
                    leftColumn.Item().AlignLeft().Text($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();
                });
            });

            column.Item().PaddingVertical(CompactSettings.SectionSpacing);
        }

        /// <summary>
        /// إضافة جدول الطلبات المضغوط مع المدفوع والمتبقي
        /// </summary>
        public static void AddCompactOrdersTable<T>(this ColumnDescriptor column, IEnumerable<T> orders,
            Func<T, string> getOrderNumber,
            Func<T, DateTime> getOrderDate,
            Func<T, string> getFabricType,
            Func<T, string> getColor,
            Func<T, decimal> getQuantity,
            Func<T, decimal> getPricePerMeter,
            Func<T, decimal> getTotalAmount,
            Func<T, decimal> getPaidAmount,
            Func<T, decimal> getRemainingAmount)
        {
            column.Item().Table(table =>
            {
                // تعريف الأعمدة المضغوطة مع إضافة المدفوع والمتبقي
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(2f);   // المتبقي
                    columns.RelativeColumn(2f);   // المدفوع
                    columns.RelativeColumn(2f);   // الإجمالي
                    columns.RelativeColumn(1.5f); // السعر/متر
                    columns.RelativeColumn(1.2f); // الكمية
                    columns.RelativeColumn(2.8f); // نوع القماش + اللون
                    columns.RelativeColumn(1.5f); // التاريخ
                    columns.RelativeColumn(1.5f); // رقم الطلب
                });

                // رأس الجدول
                table.Header(header =>
                {
                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("المتبقي")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("المدفوع")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("الإجمالي")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("السعر/متر")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("الكمية")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("المنتج")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("التاريخ")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("رقم الطلب")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();
                });

                // بيانات الجدول
                foreach (var order in orders)
                {
                    var remainingAmount = getRemainingAmount(order);
                    var paidAmount = getPaidAmount(order);

                    // المتبقي - لون أحمر إذا كان هناك مبلغ متبقي
                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{remainingAmount:N2}")
                        .FontSize(CompactSettings.TableFontSize)
                        .FontColor(remainingAmount > 0 ? QuestColors.Red.Medium : QuestColors.Green.Medium)
                        .DirectionFromRightToLeft();

                    // المدفوع - لون أخضر إذا تم الدفع
                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{paidAmount:N2}")
                        .FontSize(CompactSettings.TableFontSize)
                        .FontColor(paidAmount > 0 ? QuestColors.Green.Medium : QuestColors.Grey.Medium)
                        .DirectionFromRightToLeft();

                    // الإجمالي
                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getTotalAmount(order):N2}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getPricePerMeter(order):N2}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getQuantity(order):N1}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    // دمج نوع القماش واللون في عمود واحد
                    var fabricColor = $"{getFabricType(order)}";
                    var color = getColor(order);
                    if (!string.IsNullOrEmpty(color))
                        fabricColor += $" - {color}";

                    table.Cell().Element(CompactCellStyle).AlignRight()
                        .Text(fabricColor)
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getOrderDate(order):MM/dd}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text(getOrderNumber(order))
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();
                }
            });
        }

        /// <summary>
        /// إضافة جدول الطلبات المضغوط البسيط (للتقارير العامة)
        /// </summary>
        public static void AddSimpleCompactOrdersTable<T>(this ColumnDescriptor column, IEnumerable<T> orders,
            Func<T, string> getOrderNumber,
            Func<T, DateTime> getOrderDate,
            Func<T, string> getFabricType,
            Func<T, string> getColor,
            Func<T, decimal> getQuantity,
            Func<T, decimal> getPricePerMeter,
            Func<T, decimal> getTotalAmount)
        {
            column.Item().Table(table =>
            {
                // تعريف الأعمدة المضغوطة - دمج نوع القماش واللون
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(2.5f); // المبلغ الإجمالي
                    columns.RelativeColumn(2f);   // السعر/متر
                    columns.RelativeColumn(1.5f); // الكمية
                    columns.RelativeColumn(3f);   // نوع القماش + اللون
                    columns.RelativeColumn(2f);   // التاريخ
                    columns.RelativeColumn(2f);   // رقم الطلب
                });

                // رأس الجدول
                table.Header(header =>
                {
                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("المبلغ الإجمالي")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("السعر/متر")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("الكمية")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("المنتج")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("التاريخ")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();

                    header.Cell().Element(CompactHeaderCellStyle).AlignCenter()
                        .Text("رقم الطلب")
                        .FontSize(CompactSettings.HeaderFontSize)
                        .Bold()
                        .DirectionFromRightToLeft();
                });

                // بيانات الجدول
                foreach (var order in orders)
                {
                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getTotalAmount(order):N2}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getPricePerMeter(order):N2}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getQuantity(order):N1}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    // دمج نوع القماش واللون في عمود واحد
                    var fabricColor = $"{getFabricType(order)}";
                    var color = getColor(order);
                    if (!string.IsNullOrEmpty(color))
                        fabricColor += $" - {color}";

                    table.Cell().Element(CompactCellStyle).AlignRight()
                        .Text(fabricColor)
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text($"{getOrderDate(order):MM/dd}")
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();

                    table.Cell().Element(CompactCellStyle).AlignCenter()
                        .Text(getOrderNumber(order))
                        .FontSize(CompactSettings.TableFontSize)
                        .DirectionFromRightToLeft();
                }
            });
        }

        /// <summary>
        /// إضافة ملخص مالي مضغوط
        /// </summary>
        public static void AddCompactFinancialSummary(this ColumnDescriptor column, 
            decimal totalAmount, decimal paidAmount, decimal remainingAmount)
        {
            column.Item().PaddingVertical(CompactSettings.SectionSpacing);
            
            column.Item().AlignRight().Table(table =>
            {
                table.ColumnsDefinition(columns =>
                {
                    columns.RelativeColumn(2);
                    columns.RelativeColumn(3);
                });

                // الإجمالي
                table.Cell().Element(CompactCellStyle).AlignCenter()
                    .Text($"{totalAmount:N2} ج.م")
                    .FontSize(CompactSettings.HeaderFontSize)
                    .Bold();
                table.Cell().Element(CompactCellStyle).AlignRight()
                    .Text("إجمالي المبلغ")
                    .FontSize(CompactSettings.HeaderFontSize)
                    .Bold()
                    .DirectionFromRightToLeft();

                // المدفوع
                table.Cell().Element(CompactCellStyle).AlignCenter()
                    .Text($"{paidAmount:N2} ج.م")
                    .FontSize(CompactSettings.HeaderFontSize)
                    .FontColor(QuestColors.Green.Medium);
                table.Cell().Element(CompactCellStyle).AlignRight()
                    .Text("المدفوع")
                    .FontSize(CompactSettings.HeaderFontSize)
                    .DirectionFromRightToLeft();

                // المتبقي
                table.Cell().Element(CompactCellStyle).AlignCenter()
                    .Text($"{remainingAmount:N2} ج.م")
                    .FontSize(CompactSettings.HeaderFontSize)
                    .FontColor(remainingAmount > 0 ? QuestColors.Red.Medium : QuestColors.Green.Medium);
                table.Cell().Element(CompactCellStyle).AlignRight()
                    .Text("المتبقي")
                    .FontSize(CompactSettings.HeaderFontSize)
                    .DirectionFromRightToLeft();
            });
        }
    }
}
