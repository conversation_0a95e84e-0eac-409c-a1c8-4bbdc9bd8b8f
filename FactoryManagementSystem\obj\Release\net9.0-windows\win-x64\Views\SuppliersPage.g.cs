﻿#pragma checksum "..\..\..\..\..\Views\SuppliersPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "05B8C6010BD3D31DF21BBE57621D75BBD8BCF7DD"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FactoryManagementSystem.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FactoryManagementSystem.Views {
    
    
    /// <summary>
    /// SuppliersPage
    /// </summary>
    public partial class SuppliersPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatusFilter;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddSupplier;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddSampleData;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEditSupplier;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteSupplier;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSupplierOrders;
        
        #line default
        #line hidden
        
        
        #line 159 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSupplierPayments;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSupplierStatement;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshSuppliers;
        
        #line default
        #line hidden
        
        
        #line 285 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgSuppliers;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalSuppliers;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\..\Views\SuppliersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtActiveSuppliers;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FactoryManagementSystem;V3.0.0.0;component/views/supplierspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\SuppliersPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 40 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.TxtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CmbStatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 54 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.CmbStatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbStatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnAddSupplier = ((System.Windows.Controls.Button)(target));
            
            #line 67 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnAddSupplier.Click += new System.Windows.RoutedEventHandler(this.BtnAddSupplier_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnAddSampleData = ((System.Windows.Controls.Button)(target));
            
            #line 78 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnAddSampleData.Click += new System.Windows.RoutedEventHandler(this.BtnAddSampleData_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnEditSupplier = ((System.Windows.Controls.Button)(target));
            
            #line 101 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnEditSupplier.Click += new System.Windows.RoutedEventHandler(this.BtnEditSupplier_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnDeleteSupplier = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnDeleteSupplier.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteSupplier_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnSupplierOrders = ((System.Windows.Controls.Button)(target));
            
            #line 141 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnSupplierOrders.Click += new System.Windows.RoutedEventHandler(this.BtnSupplierOrders_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnSupplierPayments = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnSupplierPayments.Click += new System.Windows.RoutedEventHandler(this.BtnSupplierPayments_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnSupplierStatement = ((System.Windows.Controls.Button)(target));
            
            #line 197 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnSupplierStatement.Click += new System.Windows.RoutedEventHandler(this.BtnSupplierStatement_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnRefreshSuppliers = ((System.Windows.Controls.Button)(target));
            
            #line 226 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.BtnRefreshSuppliers.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshSuppliers_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DgSuppliers = ((System.Windows.Controls.DataGrid)(target));
            
            #line 287 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.DgSuppliers.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DgSuppliers_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 288 "..\..\..\..\..\Views\SuppliersPage.xaml"
            this.DgSuppliers.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.DgSuppliers_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TxtTotalSuppliers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtActiveSuppliers = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

