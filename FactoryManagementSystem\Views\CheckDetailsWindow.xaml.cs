using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace FactoryManagementSystem.Views;

public partial class CheckDetailsWindow : Window
{
    private Check _check;
    private List<TransactionViewModel> _transactions = new();

    public CheckDetailsWindow(Check check)
    {
        InitializeComponent();
        _check = check;

        // التأكد من تحميل النافذة بالكامل قبل تحميل البيانات
        Loaded += (s, e) =>
        {
            LoadCheckDetails();
            LoadTransactionHistory();
        };
    }

    private void LoadCheckDetails()
    {
        try
        {
            // التحقق من وجود الشيك
            if (_check == null)
            {
                MessageBox.Show("بيانات الشيك غير متاحة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // التحقق من وجود العناصر قبل الوصول إليها
            if (TxtCheckNumber != null) TxtCheckNumber.Text = _check.CheckNumber ?? "";
            if (TxtBankName != null) TxtBankName.Text = _check.BankName ?? "";
            if (TxtAmount != null) TxtAmount.Text = $"{_check.Amount:N2} ج.م";
            if (TxtCheckDate != null) TxtCheckDate.Text = _check.CheckDate.ToString("dd/MM/yyyy");
            if (TxtDueDate != null) TxtDueDate.Text = _check.DueDate.ToString("dd/MM/yyyy");

            if (TxtType != null) TxtType.Text = _check.Type == CheckType.Incoming ? "وارد (من عميل)" : "صادر (لمورد)";

            // Load party name
            using var context = new FactoryDbContext();
            string partyName = "-";

            if (_check.Type == CheckType.Incoming && _check.CustomerId.HasValue)
            {
                var customer = context.Customers.FirstOrDefault(c => c.CustomerId == _check.CustomerId);
                partyName = customer?.Name ?? "-";
            }
            else if (_check.Type == CheckType.Outgoing && _check.SupplierId.HasValue)
            {
                var supplier = context.Suppliers.FirstOrDefault(s => s.SupplierId == _check.SupplierId);
                partyName = supplier?.Name ?? _check.PayeeName ?? "-";
            }
            else
            {
                partyName = _check.PayeeName ?? "-";
            }

            if (TxtPartyName != null) TxtPartyName.Text = partyName;
            if (TxtStatus != null) TxtStatus.Text = GetStatusText(_check.Status);
            if (TxtCashedDate != null) TxtCashedDate.Text = _check.CashedDate?.ToString("dd/MM/yyyy HH:mm") ?? "-";
            if (TxtLastModified != null) TxtLastModified.Text = _check.LastModifiedDate?.ToString("dd/MM/yyyy HH:mm") ?? "-";
            if (TxtNotes != null) TxtNotes.Text = _check.Notes ?? "لا توجد ملاحظات";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل تفاصيل الشيك:\n{ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LoadTransactionHistory()
    {
        try
        {
            // التحقق من وجود الشيك
            if (_check == null || string.IsNullOrEmpty(_check.CheckNumber))
            {
                if (DgTransactions != null)
                {
                    DgTransactions.ItemsSource = new List<TransactionViewModel>();
                }
                return;
            }

            using var context = new FactoryDbContext();
            var transactions = context.CashTransactions
                .Where(t => t.ReferenceNumber == _check.CheckNumber)
                .OrderByDescending(t => t.TransactionDate)
                .Select(t => new TransactionViewModel
                {
                    TransactionDate = t.TransactionDate,
                    TypeText = GetTransactionTypeText(t.Type),
                    Amount = t.Amount,
                    Description = t.Description ?? ""
                })
                .ToList();

            _transactions = transactions;

            // التحقق من وجود DataGrid قبل تعيين البيانات
            if (DgTransactions != null)
            {
                DgTransactions.ItemsSource = _transactions;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل سجل الحركات:\n{ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);

            // تعيين قائمة فارغة في حالة الخطأ
            if (DgTransactions != null)
            {
                DgTransactions.ItemsSource = new List<TransactionViewModel>();
            }
        }
    }

    private string GetStatusText(CheckStatus status)
    {
        return status switch
        {
            CheckStatus.Pending => "مستحق (معلق)",
            CheckStatus.Cashed => "تم الصرف",
            CheckStatus.Returned => "مرتد",
            CheckStatus.Cancelled => "ملغي",
            _ => "غير معروف"
        };
    }

    private string GetTransactionTypeText(TransactionType type)
    {
        return type switch
        {
            TransactionType.Income => "إيراد",
            TransactionType.Expense => "مصروف",
            _ => "غير معروف"
        };
    }

    private void BtnPrint_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                var printDocument = new System.Windows.Documents.FlowDocument();
                
                // Header
                var headerParagraph = new System.Windows.Documents.Paragraph();
                headerParagraph.Inlines.Add(new System.Windows.Documents.Run("تفاصيل الشيك"));
                headerParagraph.FontSize = 18;
                headerParagraph.FontWeight = FontWeights.Bold;
                headerParagraph.TextAlignment = TextAlignment.Center;
                printDocument.Blocks.Add(headerParagraph);
                
                // Check details
                var detailsParagraph = new System.Windows.Documents.Paragraph();
                detailsParagraph.Inlines.Add(new System.Windows.Documents.Run(
                    $"رقم الشيك: {_check.CheckNumber}\n" +
                    $"البنك: {_check.BankName}\n" +
                    $"المبلغ: {_check.Amount:N2} ج.م\n" +
                    $"تاريخ الشيك: {_check.CheckDate:dd/MM/yyyy}\n" +
                    $"تاريخ الاستحقاق: {_check.DueDate:dd/MM/yyyy}\n" +
                    $"الحالة: {GetStatusText(_check.Status)}\n" +
                    $"الملاحظات: {_check.Notes ?? "لا توجد"}"
                ));
                printDocument.Blocks.Add(detailsParagraph);
                
                // Transaction history
                if (_transactions.Any())
                {
                    var historyParagraph = new System.Windows.Documents.Paragraph();
                    historyParagraph.Inlines.Add(new System.Windows.Documents.Run("\nسجل الحركات:"));
                    historyParagraph.FontWeight = FontWeights.Bold;
                    printDocument.Blocks.Add(historyParagraph);
                    
                    foreach (var transaction in _transactions)
                    {
                        var transParagraph = new System.Windows.Documents.Paragraph();
                        transParagraph.Inlines.Add(new System.Windows.Documents.Run(
                            $"{transaction.TransactionDate:dd/MM/yyyy HH:mm} - {transaction.TypeText} - {transaction.Amount:N2} ج.م - {transaction.Description}"
                        ));
                        printDocument.Blocks.Add(transParagraph);
                    }
                }

                printDialog.PrintDocument(((System.Windows.Documents.IDocumentPaginatorSource)printDocument).DocumentPaginator, "تفاصيل الشيك");
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء الطباعة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnExport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Filter = "ملف PDF (*.pdf)|*.pdf",
                FileName = $"تفاصيل_الشيك_{_check.CheckNumber}_{DateTime.Now:yyyyMMdd}.pdf"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                // Simple text export (in a real application, you'd use a PDF library)
                var content = $"تفاصيل الشيك\n\n" +
                             $"رقم الشيك: {_check.CheckNumber}\n" +
                             $"البنك: {_check.BankName}\n" +
                             $"المبلغ: {_check.Amount:N2} ج.م\n" +
                             $"تاريخ الشيك: {_check.CheckDate:dd/MM/yyyy}\n" +
                             $"تاريخ الاستحقاق: {_check.DueDate:dd/MM/yyyy}\n" +
                             $"الحالة: {GetStatusText(_check.Status)}\n" +
                             $"الملاحظات: {_check.Notes ?? "لا توجد"}\n\n" +
                             $"سجل الحركات:\n";

                foreach (var transaction in _transactions)
                {
                    content += $"{transaction.TransactionDate:dd/MM/yyyy HH:mm} - {transaction.TypeText} - {transaction.Amount:N2} ج.م - {transaction.Description}\n";
                }

                System.IO.File.WriteAllText(saveFileDialog.FileName.Replace(".pdf", ".txt"), content);
                MessageBox.Show("تم تصدير البيانات بنجاح.", "تم التصدير", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء التصدير:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}

public class TransactionViewModel
{
    public DateTime TransactionDate { get; set; }
    public string TypeText { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string Description { get; set; } = string.Empty;
} 