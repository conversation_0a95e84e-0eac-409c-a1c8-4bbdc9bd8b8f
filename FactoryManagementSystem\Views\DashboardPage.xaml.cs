using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;

namespace FactoryManagementSystem.Views;

/// <summary>
/// لوحة التحكم الرئيسية
/// </summary>
public partial class DashboardPage : Page
{
    private readonly FactoryDbContext _context;
    public ObservableCollection<RecentActivity> RecentActivities { get; set; }

    public DashboardPage()
    {
        InitializeComponent();
        _context = new FactoryDbContext();
        RecentActivities = new ObservableCollection<RecentActivity>();
        DgRecentActivities.ItemsSource = RecentActivities;
        
        LoadDashboardData();
    }

    private async void LoadDashboardData()
    {
        try
        {
            await LoadStatistics();
            await LoadRecentActivities();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات لوحة التحكم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadStatistics()
    {
        // إجمالي العملاء
        var totalCustomers = await _context.Customers.CountAsync(c => c.IsActive);
        TxtTotalCustomers.Text = totalCustomers.ToString();

        // الشيكات المستحقة خلال أسبوع
        var dueDate = DateTime.Now.AddDays(7);
        var dueChecks = await _context.Checks
            .Where(c => c.Status == CheckStatus.Pending && c.DueDate <= dueDate)
            .SumAsync(c => c.Amount);
        TxtDueChecks.Text = $"{dueChecks:N2} ج.م";

        // مدفوعات اليوم
        var today = DateTime.Today;
        var todayPayments = await _context.CashTransactions
            .Where(t => t.TransactionDate.Date == today && t.Type == TransactionType.Income)
            .SumAsync(t => t.Amount);
        TxtTodayPayments.Text = $"{todayPayments:N2} ج.م";

        // رصيد الخزينة (إجمالي الإيرادات - إجمالي المصروفات)
        var totalIncome = await _context.CashTransactions
            .Where(t => t.Type == TransactionType.Income)
            .SumAsync(t => t.Amount);
        var totalExpense = await _context.CashTransactions
            .Where(t => t.Type == TransactionType.Expense)
            .SumAsync(t => t.Amount);
        var cashBalance = totalIncome - totalExpense;
        TxtCashBalance.Text = $"{cashBalance:N2} ج.م";

        // الطلبات المعلقة
        var pendingOrders = await _context.CustomerOrders
            .CountAsync(o => o.Status == OrderStatus.Pending || o.Status == OrderStatus.InProgress);
        TxtPendingOrders.Text = $"{pendingOrders} طلب";

        // العمال الحاضرين اليوم
        var presentEmployees = await _context.EmployeeAttendances
            .Where(a => a.Date == today && a.Status == AttendanceStatus.Present)
            .CountAsync();
        TxtPresentEmployees.Text = $"{presentEmployees} عامل";

        // إجمالي الموردين
        var totalSuppliers = await _context.Suppliers.CountAsync(s => s.IsActive);
        TxtTotalSuppliers.Text = $"{totalSuppliers} مورد";
    }

    private async Task LoadRecentActivities()
    {
        RecentActivities.Clear();

        // آخر المدفوعات من العملاء
        var recentCustomerPayments = await _context.CustomerPayments
            .Include(p => p.Customer)
            .OrderByDescending(p => p.PaymentDate)
            .Take(5)
            .Select(p => new RecentActivity
            {
                Type = "دفع عميل",
                Description = $"دفعة من {p.Customer.Name}",
                Amount = p.Amount,
                Date = p.PaymentDate
            })
            .ToListAsync();

        // آخر المدفوعات للموردين
        var recentSupplierPayments = await _context.SupplierPayments
            .Include(p => p.Supplier)
            .OrderByDescending(p => p.PaymentDate)
            .Take(5)
            .Select(p => new RecentActivity
            {
                Type = "دفع مورد",
                Description = $"دفعة لـ {p.Supplier.Name}",
                Amount = p.Amount,
                Date = p.PaymentDate
            })
            .ToListAsync();

        // آخر الطلبات
        var recentOrders = await _context.CustomerOrders
            .Include(o => o.Customer)
            .OrderByDescending(o => o.OrderDate)
            .Take(5)
            .Select(o => new RecentActivity
            {
                Type = "طلب جديد",
                Description = $"طلب من {o.Customer.Name} - {o.FabricType}",
                Amount = o.TotalAmount,
                Date = o.OrderDate
            })
            .ToListAsync();

        // دمج جميع الأنشطة وترتيبها حسب التاريخ
        var allActivities = recentCustomerPayments
            .Concat(recentSupplierPayments)
            .Concat(recentOrders)
            .OrderByDescending(a => a.Date)
            .Take(10);

        foreach (var activity in allActivities)
        {
            RecentActivities.Add(activity);
        }
    }

    private void BtnAddCustomer_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addCustomerWindow = new AddEditCustomerWindow();
            // تعيين النافذة الرئيسية كمالك إذا كانت متاحة
            var mainWindow = Window.GetWindow(this);
            if (mainWindow != null && mainWindow.IsLoaded)
            {
                addCustomerWindow.Owner = mainWindow;
            }

            if (addCustomerWindow.ShowDialog() == true)
            {
                // تحديث الإحصائيات
                _ = Task.Run(LoadStatistics);
                _ = Task.Run(LoadRecentActivities);
                MessageBox.Show("تم إضافة العميل بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة العميل: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnAddOrder_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // فتح نافذة اختيار العميل أولاً
            var selectCustomerWindow = new SelectCustomerWindow();
            var mainWindow = Window.GetWindow(this);
            if (mainWindow != null && mainWindow.IsLoaded)
            {
                selectCustomerWindow.Owner = mainWindow;
            }

            if (selectCustomerWindow.ShowDialog() == true)
            {
                var selectedCustomer = selectCustomerWindow.SelectedCustomer;
                if (selectedCustomer != null)
                {
                    var addOrderWindow = new AddOrderWindow(selectedCustomer);
                    if (mainWindow != null && mainWindow.IsLoaded)
                    {
                        addOrderWindow.Owner = mainWindow;
                    }

                    if (addOrderWindow.ShowDialog() == true)
                    {
                        // تحديث الإحصائيات
                        _ = Task.Run(LoadStatistics);
                        _ = Task.Run(LoadRecentActivities);
                        MessageBox.Show("تم إضافة الطلب بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة الطلب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnCashTransaction_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // الانتقال إلى صفحة الخزينة مباشرة
            var mainWindow = Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                // الانتقال إلى صفحة الخزينة
                mainWindow.MainFrame.Navigate(new CashPage());
            }
            else
            {
                // إذا لم نجد النافذة الرئيسية، نحاول طريقة أخرى
                var currentWindow = Window.GetWindow(this) as MainWindow;
                if (currentWindow != null)
                {
                    currentWindow.MainFrame.Navigate(new CashPage());
                }
                else
                {
                    MessageBox.Show("لا يمكن الوصول للنافذة الرئيسية", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في الانتقال للخزينة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }





    ~DashboardPage()
    {
        _context?.Dispose();
    }
}

/// <summary>
/// نموذج للأنشطة الحديثة
/// </summary>
public class RecentActivity
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public DateTime Date { get; set; }
}
