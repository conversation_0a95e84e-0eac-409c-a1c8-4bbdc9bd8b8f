using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using System.Globalization;
using System.Windows.Data;
using Microsoft.EntityFrameworkCore;
using System.Windows.Media;

namespace FactoryManagementSystem.Views;

public partial class ChecksPage : Page
{
    private List<CheckViewModel> _allChecks = new();

    public ChecksPage()
    {
        InitializeComponent();
        LoadChecks();
    }

    private void LoadChecks()
    {
        try
        {
            using var context = new FactoryDbContext();
            var checksRaw = context.Checks
                .Include(c => c.Customer)
                .Include(c => c.Supplier)
                .OrderByDescending(c => c.CheckDate)
                .ToList();

            var checks = checksRaw.Select(c => new CheckViewModel
            {
                CheckId = c.CheckId,
                CheckNumber = c.CheckNumber ?? "",
                BankName = c.BankName ?? "",
                CheckDate = c.CheckDate,
                DueDate = c.DueDate,
                Amount = c.Amount,
                Type = c.Type,
                Status = c.Status,
                TypeText = c.Type == CheckType.Incoming ? "وارد (من عميل)" : "صادر (لمورد)",
                StatusText = GetStatusText(c.Status),
                PartyName = c.Type == CheckType.Incoming 
                    ? (c.Customer?.Name ?? "-") 
                    : (c.Supplier?.Name ?? c.PayeeName ?? "-"),
                Notes = c.Notes ?? ""
            }).ToList();
            
            _allChecks = checks ?? new List<CheckViewModel>();
            
            if (DgChecks != null)
            {
                DgChecks.ItemsSource = _allChecks;
            }
            
            UpdateSummaryTotals();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل الشيكات:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            _allChecks = new List<CheckViewModel>();
            if (DgChecks != null)
            {
                DgChecks.ItemsSource = _allChecks;
            }
        }
    }

    private void UpdateSummaryTotals()
    {
        try
        {
            if (_allChecks == null)
            {
                _allChecks = new List<CheckViewModel>();
            }

            var pendingTotal = _allChecks.Where(c => c != null && c.Status == CheckStatus.Pending).Sum(c => c.Amount);
            var cashedTotal = _allChecks.Where(c => c != null && c.Status == CheckStatus.Cashed).Sum(c => c.Amount);
            var outgoingTotal = _allChecks.Where(c => c != null && c.Type == CheckType.Outgoing).Sum(c => c.Amount);

            if (TxtPendingTotal != null)
                TxtPendingTotal.Text = pendingTotal.ToString("N2");
            if (TxtCashedTotal != null)
                TxtCashedTotal.Text = cashedTotal.ToString("N2");
            if (TxtOutgoingTotal != null)
                TxtOutgoingTotal.Text = outgoingTotal.ToString("N2");
        }
        catch (Exception)
        {
            // Silent fail for summary update
        }
    }

    private string GetStatusText(CheckStatus status)
    {
        switch (status)
        {
            case CheckStatus.Pending: return "مستحق (معلق)";
            case CheckStatus.Cashed: return "تم الصرف";
            case CheckStatus.Returned: return "مرتد";
            case CheckStatus.Cancelled: return "ملغي";
            default: return "غير معروف";
        }
    }

    private void CmbStatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void CmbTypeFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void ApplyFilters()
    {
        try
        {
            // حماية من null للقائمة الأساسية
            if (_allChecks == null)
            {
                _allChecks = new List<CheckViewModel>();
                return;
            }

            var filteredChecks = _allChecks.AsEnumerable();

            // فلترة الحالة - حماية من null للـ ComboBox
            if (CmbStatusFilter != null && CmbStatusFilter.SelectedItem is ComboBoxItem statusItem && statusItem.Tag?.ToString() != "All")
            {
                var statusTag = statusItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(statusTag))
                {
                    filteredChecks = filteredChecks.Where(c =>
                    {
                        if (c == null) return false;
                        return statusTag switch
                        {
                            "Pending" => c.Status == CheckStatus.Pending,
                            "Cashed" => c.Status == CheckStatus.Cashed,
                            "Outgoing" => c.Type == CheckType.Outgoing,
                            "Returned" => c.Status == CheckStatus.Returned,
                            "Cancelled" => c.Status == CheckStatus.Cancelled,
                            _ => true
                        };
                    });
                }
            }

            // فلترة النوع - حماية من null للـ ComboBox
            if (CmbTypeFilter != null && CmbTypeFilter.SelectedItem is ComboBoxItem typeItem && typeItem.Tag?.ToString() != "All")
            {
                var typeTag = typeItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(typeTag))
                {
                    filteredChecks = filteredChecks.Where(c =>
                    {
                        if (c == null) return false;
                        return typeTag switch
                        {
                            "Incoming" => c.Type == CheckType.Incoming,
                            "Outgoing" => c.Type == CheckType.Outgoing,
                            _ => true
                        };
                    });
                }
            }

            // فلترة البحث - حماية من null للـ TextBox
            if (TxtSearch != null && !string.IsNullOrWhiteSpace(TxtSearch.Text))
            {
                var searchText = TxtSearch.Text.ToLower();
                filteredChecks = filteredChecks.Where(c =>
                {
                    if (c == null) return false;
                    
                    // حماية من null لجميع الخصائص
                    var checkNumber = c.CheckNumber ?? "";
                    var partyName = c.PartyName ?? "";
                    var bankName = c.BankName ?? "";
                    var notes = c.Notes ?? "";
                    
                    return checkNumber.ToLower().Contains(searchText) ||
                           partyName.ToLower().Contains(searchText) ||
                           bankName.ToLower().Contains(searchText) ||
                           notes.ToLower().Contains(searchText);
                });
            }

            // تحديث DataGrid - حماية من null
            if (DgChecks != null)
            {
                DgChecks.ItemsSource = filteredChecks.ToList();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تصفية الشيكات:\n{ex.Message}\n\nتفاصيل إضافية: {ex.StackTrace}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefresh_Click(object sender, RoutedEventArgs e)
    {
        LoadChecks();
        
        if (TxtSearch != null)
            TxtSearch.Text = "";
        if (CmbStatusFilter != null)
            CmbStatusFilter.SelectedIndex = 0;
        if (CmbTypeFilter != null)
            CmbTypeFilter.SelectedIndex = 0;
    }

    private void BtnPrint_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                // Create a simple print document
                var printDocument = new System.Windows.Documents.FlowDocument();
                var paragraph = new System.Windows.Documents.Paragraph();
                paragraph.Inlines.Add(new System.Windows.Documents.Run("تقرير الشيكات"));
                printDocument.Blocks.Add(paragraph);

                // Add check details
                foreach (CheckViewModel check in DgChecks.ItemsSource)
                {
                    var checkParagraph = new System.Windows.Documents.Paragraph();
                    checkParagraph.Inlines.Add(new System.Windows.Documents.Run(
                        $"رقم الشيك: {check.CheckNumber} | المبلغ: {check.Amount:N2} | الحالة: {check.StatusText}"));
                    printDocument.Blocks.Add(checkParagraph);
                }

                printDialog.PrintDocument(((System.Windows.Documents.IDocumentPaginatorSource)printDocument).DocumentPaginator, "تقرير الشيكات");
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء الطباعة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnComprehensiveReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var reportWindow = new ChecksReportWindow();
            reportWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء فتح التقرير الشامل:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnReturnCheck_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (DgChecks.SelectedItem is CheckViewModel selectedCheck)
            {
                var result = MessageBox.Show($"هل تريد إرجاع الشيك رقم {selectedCheck.CheckNumber}؟", "تأكيد الإرجاع", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result != MessageBoxResult.Yes)
                    return;

                using var context = new FactoryDbContext();
                var check = context.Checks.FirstOrDefault(c => c.CheckId == selectedCheck.CheckId);
                if (check == null)
                {
                    MessageBox.Show("تعذر العثور على الشيك في قاعدة البيانات.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                check.Status = CheckStatus.Returned;
                check.LastModifiedDate = DateTime.Now;
                context.SaveChanges();

                MessageBox.Show($"تم إرجاع الشيك رقم {check.CheckNumber} بنجاح.", "تم الإرجاع", MessageBoxButton.OK, MessageBoxImage.Information);
                LoadChecks();
            }
            else
            {
                MessageBox.Show("يرجى اختيار الشيك أولاً.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء إرجاع الشيك:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    private void BtnViewParty_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (DgChecks.SelectedItem is CheckViewModel selectedCheck)
            {
                using var context = new FactoryDbContext();
                var check = context.Checks.FirstOrDefault(c => c.CheckId == selectedCheck.CheckId);
                if (check != null)
                {
                    if (check.Type == CheckType.Incoming && check.CustomerId.HasValue)
                    {
                        // Navigate to customer page
                        var customer = context.Customers.FirstOrDefault(c => c.CustomerId == check.CustomerId);
                        if (customer != null)
                        {
                            var result = MessageBox.Show($"هل تريد الانتقال إلى صفحة العميل {customer.Name}؟", "عرض العميل", MessageBoxButton.YesNo, MessageBoxImage.Question);
                            if (result == MessageBoxResult.Yes)
                            {
                                // Navigate to customer page (you'll need to implement this based on your navigation system)
                                MessageBox.Show($"سيتم الانتقال إلى صفحة العميل {customer.Name}", "انتقال", MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                    }
                    else if (check.Type == CheckType.Outgoing && check.SupplierId.HasValue)
                    {
                        // Navigate to supplier page
                        var supplier = context.Suppliers.FirstOrDefault(s => s.SupplierId == check.SupplierId);
                        if (supplier != null)
                        {
                            var result = MessageBox.Show($"هل تريد الانتقال إلى صفحة المورد {supplier.Name}؟", "عرض المورد", MessageBoxButton.YesNo, MessageBoxImage.Question);
                            if (result == MessageBoxResult.Yes)
                            {
                                // Navigate to supplier page (you'll need to implement this based on your navigation system)
                                MessageBox.Show($"سيتم الانتقال إلى صفحة المورد {supplier.Name}", "انتقال", MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                        }
                    }
                    else
                    {
                        MessageBox.Show("لا يمكن العثور على معلومات الطرف المرتبط بهذا الشيك.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("تعذر العثور على الشيك في قاعدة البيانات.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار الشيك أولاً.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء عرض الطرف:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    // Converter لزر صرف الشيك (يظهر فقط إذا كان الشيك مستحق)
    public class PendingCheckVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is CheckStatus status && status == CheckStatus.Pending)
                return Visibility.Visible;
            return Visibility.Collapsed;
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }

    // Converter لزر دفع لمورد (يظهر فقط إذا كان الشيك وارد ومستحق)
    public class IncomingCheckVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is CheckType type && type == CheckType.Incoming)
            {
                return Visibility.Visible;
            }
            return Visibility.Collapsed;
        }
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture) => throw new NotImplementedException();
    }

    // Style Selector for row colors based on check status
    public class CheckRowStyleSelector : StyleSelector
    {
        public override Style? SelectStyle(object item, DependencyObject container)
        {
            if (item is CheckViewModel check)
            {
                return check.Status switch
                {
                    CheckStatus.Pending => Application.Current.Resources["PendingRowStyle"] as Style,
                    CheckStatus.Cashed => Application.Current.Resources["CashedRowStyle"] as Style,
                    CheckStatus.Returned => Application.Current.Resources["ReturnedRowStyle"] as Style,
                    CheckStatus.Cancelled => Application.Current.Resources["CancelledRowStyle"] as Style,
                    _ => null
                };
            }
            return null;
        }
    }

    // دوال الأزرار (فارغة مؤقتاً)
    private void BtnCashCheck_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (DgChecks.SelectedItem is CheckViewModel selectedCheck)
            {
                var result = MessageBox.Show($"هل تريد صرف الشيك رقم {selectedCheck.CheckNumber} بمبلغ {selectedCheck.Amount:N2} ج.م؟\nسيتم تسجيل حركة صرف في الخزينة.", "تأكيد الصرف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result != MessageBoxResult.Yes)
                    return;

                using var context = new FactoryDbContext();
                var check = context.Checks.FirstOrDefault(c => c.CheckId == selectedCheck.CheckId);
                if (check == null)
                {
                    MessageBox.Show("تعذر العثور على الشيك في قاعدة البيانات.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }
                if (check.Status != CheckStatus.Pending)
                {
                    MessageBox.Show("لا يمكن صرف شيك غير مستحق.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                // تحديث حالة الشيك
                check.Status = CheckStatus.Cashed;
                check.CashedDate = DateTime.Now;
                check.LastModifiedDate = DateTime.Now;
                context.Checks.Update(check);

                // تسجيل حركة صرف في الخزينة
                var cashTransaction = new CashTransaction
                {
                    TransactionNumber = $"CHK-CASH-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}",
                    TransactionDate = DateTime.Now,
                    Type = TransactionType.Expense,
                    Amount = check.Amount,
                    Category = TransactionCategory.GeneralExpense,
                    Description = $"صرف شيك رقم: {check.CheckNumber} - بنك: {check.BankName}",
                    CustomerId = check.CustomerId,
                    SupplierId = check.SupplierId,
                    RelatedPartyName = selectedCheck.PartyName,
                    ReferenceNumber = check.CheckNumber,
                    Notes = $"صرف شيك من النظام - {check.Notes}"
                };
                context.CashTransactions.Add(cashTransaction);
                context.SaveChanges();

                MessageBox.Show($"تم صرف الشيك رقم {check.CheckNumber} بنجاح وتسجيل حركة في الخزينة.", "تم الصرف", MessageBoxButton.OK, MessageBoxImage.Information);
                LoadChecks();
            }
            else
            {
                MessageBox.Show("يرجى اختيار الشيك أولاً.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            string details = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            MessageBox.Show($"حدث خطأ أثناء صرف الشيك:\n{details}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
    private void BtnPayToSupplier_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (DgChecks.SelectedItem is CheckViewModel selectedCheck)
            {
                // نافذة اختيار مورد
                var supplierWindow = new SelectSupplierWindow();
                if (supplierWindow.ShowDialog() == true && supplierWindow.SelectedSupplier != null)
                {
                    var supplier = supplierWindow.SelectedSupplier;
                    var result = MessageBox.Show($"هل تريد دفع الشيك رقم {selectedCheck.CheckNumber} بمبلغ {selectedCheck.Amount:N2} ج.م للمورد {supplier.Name}؟\nسيتم تسجيل حركة صرف في الخزينة وتسجيل دفعة للمورد.", "تأكيد الدفع", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    if (result != MessageBoxResult.Yes)
                        return;

                    using var context = new FactoryDbContext();
                    var check = context.Checks.FirstOrDefault(c => c.CheckId == selectedCheck.CheckId);
                    if (check == null)
                    {
                        MessageBox.Show("تعذر العثور على الشيك في قاعدة البيانات.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }
                    if (check.Status != CheckStatus.Pending)
                    {
                        MessageBox.Show("لا يمكن دفع شيك غير مستحق.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }
                    // تحديث الشيك ليصبح صادر ويرتبط بالمورد
                    check.Type = CheckType.Outgoing;
                    check.SupplierId = supplier.SupplierId;
                    check.PayeeName = supplier.Name;
                    check.Status = CheckStatus.Cashed;
                    check.CashedDate = DateTime.Now;
                    check.LastModifiedDate = DateTime.Now;
                    context.Checks.Update(check);

                    // تسجيل دفعة للمورد (SupplierPayment)
                    var payment = new SupplierPayment
                    {
                        SupplierId = supplier.SupplierId,
                        PaymentNumber = $"CHK-{check.CheckNumber}-{DateTime.Now:yyyyMMddHHmmss}",
                        PaymentDate = DateTime.Now,
                        Amount = check.Amount,
                        PaymentMethod = PaymentMethod.Check,
                        CheckNumber = check.CheckNumber,
                        BankName = check.BankName,
                        CheckDate = check.CheckDate,
                        Notes = $"دفع شيك من صفحة الشيكات - {check.Notes}",
                        CreatedDate = DateTime.Now
                    };
                    context.SupplierPayments.Add(payment);

                    // تحديث رصيد المورد
                    supplier.Balance -= check.Amount;
                    context.Suppliers.Update(supplier);

                    // تسجيل حركة صرف في الخزينة
                    var cashTransaction = new CashTransaction
                    {
                        TransactionNumber = $"CHK-SUP-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}",
                        TransactionDate = DateTime.Now,
                        Type = TransactionType.Expense,
                        Amount = check.Amount,
                        Category = TransactionCategory.SupplierPayment,
                        Description = $"دفع شيك رقم: {check.CheckNumber} للمورد: {supplier.Name}",
                        SupplierId = supplier.SupplierId,
                        RelatedPartyName = supplier.Name,
                        ReferenceNumber = check.CheckNumber,
                        Notes = $"دفع شيك لمورد من النظام - {check.Notes}",
                        CreatedDate = DateTime.Now
                    };
                    context.CashTransactions.Add(cashTransaction);
                    context.SaveChanges();

                    MessageBox.Show($"تم دفع الشيك رقم {check.CheckNumber} للمورد {supplier.Name} وتسجيل حركة في الخزينة وتسجيل دفعة للمورد.", "تم الدفع", MessageBoxButton.OK, MessageBoxImage.Information);
                    LoadChecks();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار الشيك أولاً.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            string details = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
            MessageBox.Show($"حدث خطأ أثناء دفع الشيك لمورد:\n{details}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}

public class CheckViewModel
{
    public int CheckId { get; set; }
    public string CheckNumber { get; set; } = string.Empty;
    public string BankName { get; set; } = string.Empty;
    public DateTime CheckDate { get; set; }
    public DateTime DueDate { get; set; }
    public decimal Amount { get; set; }
    public CheckType Type { get; set; }
    public string TypeText { get; set; } = string.Empty;
    public CheckStatus Status { get; set; }
    public string StatusText { get; set; } = string.Empty;
    public string PartyName { get; set; } = string.Empty;
    public string? Notes { get; set; }
}
