using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using Microsoft.EntityFrameworkCore;

namespace FactoryManagementSystem.Views;

public partial class ChecksReportWindow : Window
{
    private List<Check> _allChecks = new();

    public ChecksReportWindow()
    {
        InitializeComponent();
        DateFrom.SelectedDate = DateTime.Now.AddMonths(-1);
        DateTo.SelectedDate = DateTime.Now;
        LoadReport();
    }

    private void LoadReport()
    {
        try
        {
            using var context = new FactoryDbContext();
            var fromDate = DateFrom.SelectedDate ?? DateTime.Now.AddMonths(-1);
            var toDate = DateTo.SelectedDate ?? DateTime.Now;

            _allChecks = context.Checks
                .Where(c => c.CheckDate >= fromDate && c.CheckDate <= toDate)
                .OrderByDescending(c => c.CheckDate)
                .ToList();

            UpdateStatistics();
            LoadTopBanks();
            LoadRecentChecks();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء تحميل التقرير:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            var totalChecks = _allChecks.Count;
            var pendingChecks = _allChecks.Where(c => c.Status == CheckStatus.Pending);
            var cashedChecks = _allChecks.Where(c => c.Status == CheckStatus.Cashed);
            var outgoingChecks = _allChecks.Where(c => c.Type == CheckType.Outgoing);
            var returnedChecks = _allChecks.Where(c => c.Status == CheckStatus.Returned);

            TxtTotalChecks.Text = totalChecks.ToString();
            TxtPendingAmount.Text = pendingChecks.Sum(c => c.Amount).ToString("N2");
            TxtCashedAmount.Text = cashedChecks.Sum(c => c.Amount).ToString("N2");
            TxtOutgoingAmount.Text = outgoingChecks.Sum(c => c.Amount).ToString("N2");

            TxtPendingCount.Text = pendingChecks.Count().ToString();
            TxtCashedCount.Text = cashedChecks.Count().ToString();
            TxtOutgoingCount.Text = outgoingChecks.Count().ToString();
            TxtReturnedCount.Text = returnedChecks.Count().ToString();
        }
        catch (Exception)
        {
            // Silent fail for statistics update
        }
    }

    private void LoadTopBanks()
    {
        try
        {
            var topBanks = _allChecks
                .GroupBy(c => c.BankName)
                .Select(g => new BankStatViewModel
                {
                    BankName = g.Key,
                    CheckCount = g.Count(),
                    TotalAmount = g.Sum(c => c.Amount)
                })
                .OrderByDescending(b => b.TotalAmount)
                .Take(10)
                .ToList();

            DgTopBanks.ItemsSource = topBanks;
        }
        catch (Exception)
        {
            // Silent fail for top banks loading
        }
    }

    private void LoadRecentChecks()
    {
        try
        {
            var recentChecks = _allChecks
                .Take(20)
                .Select(c => new RecentCheckViewModel
                {
                    CheckNumber = c.CheckNumber,
                    BankName = c.BankName,
                    Amount = c.Amount,
                    StatusText = GetStatusText(c.Status),
                    CheckDate = c.CheckDate
                })
                .ToList();

            DgRecentChecks.ItemsSource = recentChecks;
        }
        catch (Exception)
        {
            // Silent fail for recent checks loading
        }
    }

    private string GetStatusText(CheckStatus status)
    {
        return status switch
        {
            CheckStatus.Pending => "مستحق (معلق)",
            CheckStatus.Cashed => "تم الصرف",
            CheckStatus.Returned => "مرتد",
            CheckStatus.Cancelled => "ملغي",
            _ => "غير معروف"
        };
    }

    private void BtnUpdateReport_Click(object sender, RoutedEventArgs e)
    {
        LoadReport();
    }

    private void BtnPrintReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var printDialog = new PrintDialog();
            if (printDialog.ShowDialog() == true)
            {
                var printDocument = new System.Windows.Documents.FlowDocument();
                
                // Header
                var headerParagraph = new System.Windows.Documents.Paragraph();
                headerParagraph.Inlines.Add(new System.Windows.Documents.Run("تقرير الشيكات الشامل"));
                headerParagraph.FontSize = 18;
                headerParagraph.FontWeight = FontWeights.Bold;
                headerParagraph.TextAlignment = TextAlignment.Center;
                printDocument.Blocks.Add(headerParagraph);
                
                // Date range
                var dateParagraph = new System.Windows.Documents.Paragraph();
                dateParagraph.Inlines.Add(new System.Windows.Documents.Run(
                    $"من تاريخ: {DateFrom.SelectedDate:dd/MM/yyyy} إلى تاريخ: {DateTo.SelectedDate:dd/MM/yyyy}"));
                dateParagraph.TextAlignment = TextAlignment.Center;
                printDocument.Blocks.Add(dateParagraph);
                
                // Statistics
                var statsParagraph = new System.Windows.Documents.Paragraph();
                statsParagraph.Inlines.Add(new System.Windows.Documents.Run(
                    $"\nإحصائيات عامة:\n" +
                    $"إجمالي الشيكات: {TxtTotalChecks.Text}\n" +
                    $"المبلغ المعلق: {TxtPendingAmount.Text} ج.م\n" +
                    $"المبلغ المقبوض: {TxtCashedAmount.Text} ج.م\n" +
                    $"المبلغ الصادر: {TxtOutgoingAmount.Text} ج.م\n\n" +
                    $"توزيع الشيكات:\n" +
                    $"شيكات معلقة: {TxtPendingCount.Text}\n" +
                    $"شيكات مقبوضة: {TxtCashedCount.Text}\n" +
                    $"شيكات صادرة: {TxtOutgoingCount.Text}\n" +
                    $"شيكات مرتجعة: {TxtReturnedCount.Text}"
                ));
                printDocument.Blocks.Add(statsParagraph);

                printDialog.PrintDocument(((System.Windows.Documents.IDocumentPaginatorSource)printDocument).DocumentPaginator, "تقرير الشيكات الشامل");
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء الطباعة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_allChecks == null || !_allChecks.Any())
            {
                MessageBox.Show("لا توجد شيكات للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var fileName = Helpers.ExcelExportHelper.ShowSaveDialog(
                $"تقرير_الشيكات_{DateTime.Now:yyyyMMdd}.csv",
                "تصدير تقرير الشيكات");

            if (!string.IsNullOrEmpty(fileName))
            {
                var headers = new Dictionary<string, Func<Models.Check, object>>
                {
                    { "الطرف", c => c.PayeeName ?? "-" },
                    { "النوع", c => c.Type == Models.CheckType.Incoming ? "وارد" : "صادر" },
                    { "التاريخ", c => c.CheckDate.ToString("dd/MM/yyyy") },
                    { "الحالة", c => GetStatusText(c.Status) },
                    { "المبلغ", c => $"{c.Amount:N2} ج.م" },
                    { "البنك", c => c.BankName ?? "-" },
                    { "رقم الشيك", c => c.CheckNumber ?? "-" }
                };

                var summary = new Dictionary<string, decimal>
                {
                    { "إجمالي عدد الشيكات", _allChecks.Count() },
                    { "إجمالي المبالغ", _allChecks.Sum(c => c.Amount) },
                    { "الشيكات المصروفة", _allChecks.Count(c => c.Status == Models.CheckStatus.Cashed) },
                    { "الشيكات المعلقة", _allChecks.Count(c => c.Status == Models.CheckStatus.Pending) }
                };

                Helpers.ExcelExportHelper.ExportFinancialReport(
                    _allChecks,
                    headers,
                    fileName,
                    "تقرير الشيكات",
                    summary);

                MessageBox.Show($"تم تصدير {_allChecks.Count()} شيك بنجاح إلى:\n{fileName}",
                    "تصدير ناجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"حدث خطأ أثناء التصدير:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}

public class BankStatViewModel
{
    public string BankName { get; set; } = string.Empty;
    public int CheckCount { get; set; }
    public decimal TotalAmount { get; set; }
}

public class RecentCheckViewModel
{
    public string CheckNumber { get; set; } = string.Empty;
    public string BankName { get; set; } = string.Empty;
    public decimal Amount { get; set; }
    public string StatusText { get; set; } = string.Empty;
    public DateTime CheckDate { get; set; }
} 