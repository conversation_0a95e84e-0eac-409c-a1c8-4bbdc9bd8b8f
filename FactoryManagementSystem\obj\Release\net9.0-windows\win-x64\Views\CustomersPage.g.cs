﻿#pragma checksum "..\..\..\..\..\Views\CustomersPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3C3B26D64412FAA1D5741B962CFF8F916D686391"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FactoryManagementSystem.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FactoryManagementSystem.Views {
    
    
    /// <summary>
    /// CustomersPage
    /// </summary>
    public partial class CustomersPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearch;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbStatusFilter;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddCustomer;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEditCustomer;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteCustomer;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddSampleData;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCustomerOrders;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCustomerPayments;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCustomerStatement;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshCustomers;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgCustomers;
        
        #line default
        #line hidden
        
        
        #line 305 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalCustomers;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\..\Views\CustomersPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtActiveCustomers;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FactoryManagementSystem;V3.0.0.0;component/views/customerspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\CustomersPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 41 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.TxtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.CmbStatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 55 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.CmbStatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbStatusFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnAddCustomer = ((System.Windows.Controls.Button)(target));
            
            #line 68 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnAddCustomer.Click += new System.Windows.RoutedEventHandler(this.BtnAddCustomer_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnEditCustomer = ((System.Windows.Controls.Button)(target));
            
            #line 75 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnEditCustomer.Click += new System.Windows.RoutedEventHandler(this.BtnEditCustomer_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnDeleteCustomer = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnDeleteCustomer.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteCustomer_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnAddSampleData = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnAddSampleData.Click += new System.Windows.RoutedEventHandler(this.BtnAddSampleData_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnCustomerOrders = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnCustomerOrders.Click += new System.Windows.RoutedEventHandler(this.BtnCustomerOrders_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnCustomerPayments = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnCustomerPayments.Click += new System.Windows.RoutedEventHandler(this.BtnCustomerPayments_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnCustomerStatement = ((System.Windows.Controls.Button)(target));
            
            #line 198 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnCustomerStatement.Click += new System.Windows.RoutedEventHandler(this.BtnCustomerStatement_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnRefreshCustomers = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.BtnRefreshCustomers.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshCustomers_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.DgCustomers = ((System.Windows.Controls.DataGrid)(target));
            
            #line 288 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.DgCustomers.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.DgCustomers_SelectionChanged);
            
            #line default
            #line hidden
            
            #line 289 "..\..\..\..\..\Views\CustomersPage.xaml"
            this.DgCustomers.MouseDoubleClick += new System.Windows.Input.MouseButtonEventHandler(this.DgCustomers_MouseDoubleClick);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TxtTotalCustomers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtActiveCustomers = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

