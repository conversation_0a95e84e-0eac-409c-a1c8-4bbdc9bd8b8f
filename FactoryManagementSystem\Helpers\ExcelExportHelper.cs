using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;

namespace FactoryManagementSystem.Helpers
{
    /// <summary>
    /// مساعد تصدير Excel مع دعم العربية والاتجاه من اليمين للشمال
    /// </summary>
    public static class ExcelExportHelper
    {
        /// <summary>
        /// تصدير البيانات إلى ملف CSV مع دعم العربية والاتجاه من اليمين للشمال
        /// </summary>
        /// <param name="data">البيانات للتصدير</param>
        /// <param name="headers">رؤوس الأعمدة</param>
        /// <param name="fileName">اسم الملف</param>
        /// <param name="title">عنوان التقرير</param>
        /// <param name="additionalInfo">معلومات إضافية</param>
        public static void ExportToCsv<T>(
            IEnumerable<T> data,
            Dictionary<string, Func<T, object>> headers,
            string fileName,
            string title = "",
            Dictionary<string, string>? additionalInfo = null)
        {
            try
            {
                var csv = new StringBuilder();
                
                // إضافة BOM للدعم الصحيح للعربية
                csv.Append('\ufeff');
                
                // إضافة العنوان إذا كان موجود
                if (!string.IsNullOrEmpty(title))
                {
                    csv.AppendLine($"# {title}");
                    csv.AppendLine($"# تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
                    csv.AppendLine();
                }
                
                // إضافة المعلومات الإضافية
                if (additionalInfo != null)
                {
                    foreach (var info in additionalInfo)
                    {
                        csv.AppendLine($"{info.Key},{info.Value}");
                    }
                    csv.AppendLine();
                }
                
                // إضافة رؤوس الأعمدة (من اليمين للشمال)
                var headerKeys = headers.Keys.Reverse().ToList();
                csv.AppendLine(string.Join(",", headerKeys));
                
                // إضافة البيانات
                foreach (var item in data)
                {
                    var values = new List<string>();
                    foreach (var header in headerKeys)
                    {
                        var value = headers[header](item);
                        var stringValue = value?.ToString() ?? "";
                        
                        // تنظيف القيم وحمايتها من الفواصل
                        stringValue = CleanCsvValue(stringValue);
                        values.Add(stringValue);
                    }
                    csv.AppendLine(string.Join(",", values));
                }
                
                // كتابة الملف مع ترميز UTF-8
                File.WriteAllText(fileName, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير الملف: {ex.Message}");
            }
        }
        
        /// <summary>
        /// تنظيف قيمة CSV وحمايتها من الفواصل والعلامات الخاصة
        /// </summary>
        private static string CleanCsvValue(string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";
                
            // إزالة الأسطر الجديدة
            value = value.Replace("\r\n", " ").Replace("\n", " ").Replace("\r", " ");
            
            // إذا كانت القيمة تحتوي على فاصلة أو علامات اقتباس، نضعها بين علامات اقتباس
            if (value.Contains(",") || value.Contains("\"") || value.Contains("\n"))
            {
                value = value.Replace("\"", "\"\""); // مضاعفة علامات الاقتباس
                value = $"\"{value}\"";
            }
            
            return value;
        }
        
        /// <summary>
        /// عرض نافذة حفظ الملف مع الخيارات المناسبة
        /// </summary>
        public static string? ShowSaveDialog(string defaultFileName, string title = "تصدير البيانات")
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = title,
                    Filter = "ملف Excel (*.xlsx)|*.xlsx|ملف CSV (*.csv)|*.csv|ملف نصي (*.txt)|*.txt",
                    FileName = defaultFileName,
                    DefaultExt = "csv",
                    AddExtension = true
                };

                return saveDialog.ShowDialog() == true ? saveDialog.FileName : null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض نافذة الحفظ: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }
        
        /// <summary>
        /// تصدير قائمة بسيطة من النصوص
        /// </summary>
        public static void ExportSimpleList(
            IEnumerable<string> items, 
            string fileName, 
            string title = "",
            string columnHeader = "البيانات")
        {
            try
            {
                var csv = new StringBuilder();
                csv.Append('\ufeff'); // BOM
                
                if (!string.IsNullOrEmpty(title))
                {
                    csv.AppendLine($"# {title}");
                    csv.AppendLine($"# تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
                    csv.AppendLine();
                }
                
                csv.AppendLine(columnHeader);
                
                foreach (var item in items)
                {
                    csv.AppendLine(CleanCsvValue(item));
                }
                
                File.WriteAllText(fileName, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير القائمة: {ex.Message}");
            }
        }
        
        /// <summary>
        /// إنشاء تقرير مالي مع ملخص
        /// </summary>
        public static void ExportFinancialReport<T>(
            IEnumerable<T> data,
            Dictionary<string, Func<T, object>> headers,
            string fileName,
            string title,
            Dictionary<string, decimal> summary)
        {
            try
            {
                var csv = new StringBuilder();
                csv.Append('\ufeff'); // BOM
                
                // العنوان
                csv.AppendLine($"# {title}");
                csv.AppendLine($"# تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
                csv.AppendLine();
                
                // الملخص المالي
                csv.AppendLine("الملخص المالي:");
                foreach (var item in summary)
                {
                    csv.AppendLine($"{item.Key},{item.Value:N2}");
                }
                csv.AppendLine();
                
                // البيانات التفصيلية
                csv.AppendLine("البيانات التفصيلية:");
                
                // رؤوس الأعمدة
                var headerKeys = headers.Keys.Reverse().ToList();
                csv.AppendLine(string.Join(",", headerKeys));
                
                // البيانات
                foreach (var item in data)
                {
                    var values = headerKeys.Select(header => 
                        CleanCsvValue(headers[header](item)?.ToString() ?? ""));
                    csv.AppendLine(string.Join(",", values));
                }
                
                File.WriteAllText(fileName, csv.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تصدير التقرير المالي: {ex.Message}");
            }
        }
    }
}
