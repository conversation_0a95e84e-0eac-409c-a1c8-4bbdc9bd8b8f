using System;
using System.IO;
using FactoryManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace FactoryManagementSystem.Utilities
{
    /// <summary>
    /// أداة سريعة لإعادة تعيين قاعدة البيانات بدون واجهة مستخدم
    /// </summary>
    public static class QuickDatabaseReset
    {
        /// <summary>
        /// إعادة تعيين قاعدة البيانات بسرعة (للاستخدام في حالات الطوارئ)
        /// </summary>
        public static void ResetDatabase()
        {
            try
            {
                Console.WriteLine("بدء إعادة تعيين قاعدة البيانات...");

                // حذف قاعدة البيانات الحالية
                DeleteCurrentDatabase();
                Console.WriteLine("تم حذف قاعدة البيانات القديمة.");

                // إعادة إنشاء قاعدة البيانات
                RecreateDatabase();
                Console.WriteLine("تم إنشاء قاعدة البيانات الجديدة.");

                // إضافة بيانات أساسية
                SeedBasicData();
                Console.WriteLine("تم إضافة البيانات الأساسية.");

                Console.WriteLine("تم إعادة تعيين قاعدة البيانات بنجاح!");
                Console.WriteLine("المستخدم الافتراضي: admin");
                Console.WriteLine("كلمة السر الافتراضية: 123");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إعادة تعيين قاعدة البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// حذف قاعدة البيانات الحالية
        /// </summary>
        private static void DeleteCurrentDatabase()
        {
            var dbPath = GetDatabasePath();
            
            if (File.Exists(dbPath))
            {
                // إغلاق جميع الاتصالات
                GC.Collect();
                GC.WaitForPendingFinalizers();

                File.Delete(dbPath);
            }

            // حذف ملفات SQLite الإضافية
            var walFile = dbPath + "-wal";
            var shmFile = dbPath + "-shm";

            if (File.Exists(walFile))
                File.Delete(walFile);

            if (File.Exists(shmFile))
                File.Delete(shmFile);
        }

        /// <summary>
        /// إعادة إنشاء قاعدة البيانات
        /// </summary>
        private static void RecreateDatabase()
        {
            using var context = new FactoryDbContext();
            
            // حذف قاعدة البيانات إذا كانت موجودة
            context.Database.EnsureDeleted();
            
            // إنشاء قاعدة البيانات الجديدة
            context.Database.EnsureCreated();
            
            // تطبيق أي migrations معلقة
            context.Database.Migrate();
        }

        /// <summary>
        /// إضافة بيانات أساسية
        /// </summary>
        private static void SeedBasicData()
        {
            using var context = new FactoryDbContext();

            // إضافة مستخدم افتراضي
            var defaultUser = new Models.User
            {
                Username = "admin",
                Password = "123",
                Role = "Administrator",
                IsActive = true,
                CreatedDate = DateTime.Now
            };
            context.Users.Add(defaultUser);

            // إضافة عميل تجريبي
            var testCustomer = new Models.Customer
            {
                Name = "عميل تجريبي",
                Phone = "01000000000",
                Address = "عنوان تجريبي",
                City = "القاهرة",
                Balance = 0,
                IsActive = true,
                CreatedDate = DateTime.Now
            };
            context.Customers.Add(testCustomer);

            // إضافة مورد تجريبي
            var testSupplier = new Models.Supplier
            {
                Name = "مورد تجريبي",
                CompanyName = "شركة تجريبية",
                Phone = "01000000001",
                Address = "عنوان تجريبي",
                City = "الجيزة",
                Balance = 0,
                IsActive = true,
                CreatedDate = DateTime.Now
            };
            context.Suppliers.Add(testSupplier);

            context.SaveChanges();
        }

        /// <summary>
        /// الحصول على مسار قاعدة البيانات
        /// </summary>
        private static string GetDatabasePath()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            return Path.Combine(dataDirectory, "FactoryDbContext.db");
        }

        /// <summary>
        /// فحص وجود قاعدة البيانات
        /// </summary>
        public static bool DatabaseExists()
        {
            var dbPath = GetDatabasePath();
            return File.Exists(dbPath);
        }

        /// <summary>
        /// الحصول على معلومات قاعدة البيانات
        /// </summary>
        public static void ShowDatabaseInfo()
        {
            var dbPath = GetDatabasePath();
            
            Console.WriteLine("معلومات قاعدة البيانات:");
            Console.WriteLine($"المسار: {dbPath}");
            Console.WriteLine($"موجودة: {(File.Exists(dbPath) ? "نعم" : "لا")}");
            
            if (File.Exists(dbPath))
            {
                var fileInfo = new FileInfo(dbPath);
                Console.WriteLine($"الحجم: {fileInfo.Length / 1024} KB");
                Console.WriteLine($"تاريخ التعديل: {fileInfo.LastWriteTime}");
            }

            try
            {
                using var context = new FactoryDbContext();
                var usersCount = context.Users.Count();
                var customersCount = context.Customers.Count();
                var suppliersCount = context.Suppliers.Count();
                
                Console.WriteLine($"عدد المستخدمين: {usersCount}");
                Console.WriteLine($"عدد العملاء: {customersCount}");
                Console.WriteLine($"عدد الموردين: {suppliersCount}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في قراءة البيانات: {ex.Message}");
            }
        }
    }
}
