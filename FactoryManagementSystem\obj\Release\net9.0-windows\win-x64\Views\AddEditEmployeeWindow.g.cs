﻿#pragma checksum "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DAD51C59E175FBC6E0F128BFA16171C744412031"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using FactoryManagementSystem.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FactoryManagementSystem.Views {
    
    
    /// <summary>
    /// AddEditEmployeeWindow
    /// </summary>
    public partial class AddEditEmployeeWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTitle;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtName;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtEmployeeNumber;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNationalId;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtPhone;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpBirthDate;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtAddress;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpHireDate;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtPosition;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbDepartment;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBasicSalary;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtHourlyRate;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWorkingHoursPerDay;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWorkingDaysPerWeek;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkIsActive;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 255 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FactoryManagementSystem;V3.0.0.0;component/views/addeditemployeewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtEmployeeNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TxtNationalId = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtPhone = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.DpBirthDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.TxtAddress = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.DpHireDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.TxtPosition = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CmbDepartment = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.TxtBasicSalary = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.TxtHourlyRate = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.TxtWorkingHoursPerDay = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.TxtWorkingDaysPerWeek = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ChkIsActive = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 253 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 263 "..\..\..\..\..\Views\AddEditEmployeeWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

