using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace FactoryManagementSystem.Models
{
    /// <summary>
    /// نموذج العامل
    /// </summary>
    public class Employee
    {
        [Key]
        public int EmployeeId { get; set; }

        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(20)]
        public string EmployeeNumber { get; set; } = string.Empty;

        [StringLength(20)]
        public string? NationalId { get; set; }

        [StringLength(15)]
        public string? Phone { get; set; }

        [StringLength(200)]
        public string? Address { get; set; }

        public DateTime? BirthDate { get; set; }

        public DateTime HireDate { get; set; } = DateTime.Now;

        [StringLength(100)]
        public string Position { get; set; } = "غير محدد"; // المنصب

        [StringLength(50)]
        public string Department { get; set; } = "الإنتاج"; // القسم

        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; } // الراتب الأساسي

        [Column(TypeName = "decimal(18,2)")]
        public decimal HourlyRate { get; set; } = 0; // أجر الساعة للعمل الإضافي

        public int WorkingHoursPerDay { get; set; } = 8; // ساعات العمل اليومية

        public int WorkingDaysPerWeek { get; set; } = 6; // أيام العمل الأسبوعية

        public bool IsActive { get; set; } = true;

        [StringLength(500)]
        public string? Notes { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? LastModifiedDate { get; set; }

        // العلاقات
        public virtual ICollection<EmployeeAttendance> Attendances { get; set; } = new List<EmployeeAttendance>();
        public virtual ICollection<EmployeeAdvance> Advances { get; set; } = new List<EmployeeAdvance>();
        public virtual ICollection<EmployeeSalary> Salaries { get; set; } = new List<EmployeeSalary>();
    }
}
