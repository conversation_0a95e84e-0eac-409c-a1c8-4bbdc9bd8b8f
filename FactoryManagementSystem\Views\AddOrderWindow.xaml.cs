using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;

namespace FactoryManagementSystem.Views
{
    /// <summary>
    /// نموذج عنصر الطلب - لدعم المنتجات المتعددة
    /// </summary>
    public class OrderProductViewModel : INotifyPropertyChanged
    {
        private string _fabricType = string.Empty;
        private string? _color;
        private decimal _quantity = 1;
        private decimal _pricePerMeter = 0;
        private decimal _totalAmount = 0;
        private string? _notes;

        public string FabricType
        {
            get => _fabricType;
            set
            {
                _fabricType = value;
                OnPropertyChanged();
                CalculateTotal();
            }
        }

        public string? Color
        {
            get => _color;
            set
            {
                _color = value;
                OnPropertyChanged();
            }
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged();
                CalculateTotal();
            }
        }

        public decimal PricePerMeter
        {
            get => _pricePerMeter;
            set
            {
                _pricePerMeter = value;
                OnPropertyChanged();
                CalculateTotal();
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            private set
            {
                _totalAmount = value;
                OnPropertyChanged();
            }
        }

        public string? Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        private void CalculateTotal()
        {
            TotalAmount = Quantity * PricePerMeter;
        }

        public bool IsValid(out string errorMessage)
        {
            errorMessage = string.Empty;

            if (string.IsNullOrWhiteSpace(FabricType))
            {
                errorMessage = "يرجى إدخال نوع القماش";
                return false;
            }

            if (FabricType.Length > 100)
            {
                errorMessage = "نوع القماش لا يجب أن يتجاوز 100 حرف";
                return false;
            }

            if (Quantity <= 0)
            {
                errorMessage = "يرجى إدخال كمية صحيحة أكبر من صفر";
                return false;
            }

            if (PricePerMeter <= 0)
            {
                errorMessage = "يرجى إدخال سعر صحيح أكبر من صفر";
                return false;
            }

            if (!string.IsNullOrEmpty(Color) && Color.Length > 50)
            {
                errorMessage = "اللون لا يجب أن يتجاوز 50 حرف";
                return false;
            }

            if (!string.IsNullOrEmpty(Notes) && Notes.Length > 500)
            {
                errorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف";
                return false;
            }

            return true;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
    public partial class AddOrderWindow : Window
    {
        private Customer _customer = null!;
        private FactoryDbContext _context = null!;
        private CustomerOrder? _orderToEdit;
        private bool _isEditMode;
        public CustomerOrder? NewOrder { get; private set; }
        public bool OrderSaved { get; private set; } = false;

        // مجموعة المنتجات في الطلب
        public ObservableCollection<OrderProductViewModel> OrderItems { get; set; } = new();

        // المنتج الحالي قيد التحرير
        private OrderProductViewModel? _currentEditingItem;

        // Constructor للإضافة
        public AddOrderWindow(Customer customer)
        {
            _isEditMode = false;
            _orderToEdit = null;
            InitializeWindow(customer);
        }

        // Constructor للتعديل
        public AddOrderWindow(Customer customer, CustomerOrder orderToEdit)
        {
            _isEditMode = true;
            _orderToEdit = orderToEdit;
            InitializeWindow(customer);
        }

        private void InitializeWindow(Customer customer)
        {
            try
            {
                InitializeComponent();
                _customer = customer ?? throw new ArgumentNullException(nameof(customer));
                _context = new FactoryDbContext();

                // ربط DataGrid بالمجموعة
                if (DgOrderItems != null)
                    DgOrderItems.ItemsSource = OrderItems;

                LoadCustomerInfo();
                InitializeForm();

                // إذا كان في وضع التعديل، تحميل بيانات الطلب
                if (_isEditMode && _orderToEdit != null)
                {
                    LoadOrderForEdit();
                }

                UpdateOrderSummary();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في Constructor: {ex.Message}");
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadOrderForEdit()
        {
            try
            {
                if (_orderToEdit == null) return;

                // تحديث العنوان
                Title = "تعديل الطلب";

                // تحميل بيانات الطلب الأساسية
                if (TxtOrderNumber != null)
                    TxtOrderNumber.Text = _orderToEdit.OrderNumber;
                if (DpOrderDate != null)
                    DpOrderDate.SelectedDate = _orderToEdit.OrderDate;

                // تحميل منتجات الطلب من قاعدة البيانات
                using var context = new FactoryDbContext();
                var orderItems = context.OrderItems
                    .Where(item => item.OrderId == _orderToEdit.OrderId)
                    .ToList();

                // تحويل منتجات الطلب إلى OrderProductViewModel
                OrderItems.Clear();
                foreach (var item in orderItems)
                {
                    var productViewModel = new OrderProductViewModel
                    {
                        FabricType = item.ProductName,
                        Color = item.Color,
                        Quantity = item.Quantity,
                        PricePerMeter = item.UnitPrice,
                        Notes = item.Specifications ?? ""
                    };
                    OrderItems.Add(productViewModel);
                }

                UpdateOrderSummary();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في LoadOrderForEdit: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل بيانات الطلب: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCustomerInfo()
        {
            try
            {
                if (TxtCustomerName != null)
                    TxtCustomerName.Text = _customer.Name;
                if (TxtCustomerPhone != null)
                    TxtCustomerPhone.Text = _customer.Phone ?? "غير محدد";
                if (TxtCustomerBalance != null)
                    TxtCustomerBalance.Text = $"{_customer.Balance:N2} ج.م";

                // عرض حد الائتمان والمتاح
                if (TxtAvailableCredit != null)
                {
                    var availableCredit = _customer.CreditLimit + _customer.Balance; // Balance سالب للمديونية
                    TxtAvailableCredit.Text = $"حد الائتمان: {_customer.CreditLimit:N2} ج.م | المتاح: {availableCredit:N2} ج.م";
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في LoadCustomerInfo: {ex.Message}");
            }
        }

        private void InitializeForm()
        {
            try
            {
                // إنشاء رقم طلب تلقائي
                if (TxtOrderNumber != null)
                    TxtOrderNumber.Text = GenerateOrderNumber();

                // تعيين تاريخ اليوم
                if (DpOrderDate != null)
                    DpOrderDate.SelectedDate = DateTime.Now;

                // تعيين قيم افتراضية
                if (TxtQuantity != null)
                    TxtQuantity.Text = "1.00";
                if (TxtUnitPrice != null)
                    TxtUnitPrice.Text = "0.00";
                if (CmbColor != null && CmbColor.Items.Count > 0)
                    CmbColor.SelectedIndex = -1; // لا نختار لون افتراضي (اختياري)
                if (CmbStatus != null && CmbStatus.Items.Count > 0)
                    CmbStatus.SelectedIndex = 0; // جديد

                // التركيز على وصف الطلب
                if (TxtDescription != null)
                    TxtDescription.Focus();

                // حساب الإجمالي
                CalculateTotal();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في InitializeForm: {ex.Message}");
            }
        }

        private string GenerateOrderNumber()
        {
            // إنشاء رقم طلب فريد بناءً على التاريخ والوقت مع رقم عشوائي
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var random = new Random().Next(100, 999);
            return $"CO-{timestamp}-{random}";
        }

        private void TxtQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void TxtUnitPrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotal();
        }

        private void CalculateTotal()
        {
            try
            {
                if (TxtQuantity != null && TxtUnitPrice != null && TxtTotalAmount != null)
                {
                    if (decimal.TryParse(TxtQuantity.Text, out decimal quantity) &&
                        decimal.TryParse(TxtUnitPrice.Text, out decimal unitPrice))
                    {
                        var total = quantity * unitPrice;
                        TxtTotalAmount.Text = $"{total:N2} ج.م";
                    }
                    else
                    {
                        TxtTotalAmount.Text = "0.00 ج.م";
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في CalculateTotal: {ex.Message}");
                if (TxtTotalAmount != null)
                    TxtTotalAmount.Text = "0.00 ج.م";
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // تعطيل الزر أثناء الحفظ
                BtnSave.IsEnabled = false;
                BtnSave.Content = _isEditMode ? "⏳ جاري التحديث..." : "⏳ جاري الحفظ...";

                // التحقق من صحة البيانات
                if (!ValidateInput())
                {
                    BtnSave.IsEnabled = true;
                    BtnSave.Content = _isEditMode ? "💾 حفظ التعديلات" : "💾 حفظ الطلب";
                    return;
                }

                // حساب الإجمالي الكلي للطلب
                var totalAmount = OrderItems.Sum(item => item.TotalAmount);

                // التحقق من حد الائتمان (فقط للطلبات الجديدة أو إذا زاد المبلغ)
                if (!_isEditMode || (_orderToEdit != null && totalAmount > _orderToEdit.TotalAmount))
                {
                    var amountToCheck = _isEditMode ? totalAmount - _orderToEdit!.TotalAmount : totalAmount;
                    if (!CheckCreditLimit(amountToCheck))
                    {
                        BtnSave.IsEnabled = true;
                        BtnSave.Content = _isEditMode ? "💾 حفظ التعديلات" : "💾 حفظ الطلب";
                        return;
                    }
                }

                if (_isEditMode)
                {
                    await UpdateExistingOrder();
                }
                else
                {
                    await CreateNewOrder();
                }
            }
            catch (Exception ex)
            {
                // تسجيل تفاصيل الخطأ للمطور
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الطلب:");
                System.Diagnostics.Debug.WriteLine($"   الرسالة: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"   النوع: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    System.Diagnostics.Debug.WriteLine($"   الخطأ الداخلي: {ex.InnerException.Message}");
                }
                System.Diagnostics.Debug.WriteLine($"   Stack Trace: {ex.StackTrace}");

                // رسالة خطأ للمستخدم
                var errorMessage = "حدث خطأ أثناء حفظ الطلب:\n\n";
                if (ex.InnerException != null)
                {
                    errorMessage += $"التفاصيل: {ex.InnerException.Message}";
                }
                else
                {
                    errorMessage += ex.Message;
                }

                MessageBox.Show(errorMessage, "خطأ في الحفظ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // إعادة تفعيل الزر
                BtnSave.IsEnabled = true;
                BtnSave.Content = _isEditMode ? "💾 حفظ التعديلات" : "💾 حفظ الطلب";
            }
        }

        private async Task UpdateExistingOrder()
        {
            if (_orderToEdit == null) return;

            using var context = new FactoryDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();
            try
            {
                // حذف منتجات الطلب القديمة
                var existingItems = context.OrderItems
                    .Where(item => item.OrderId == _orderToEdit.OrderId);
                context.OrderItems.RemoveRange(existingItems);

                // حساب الفرق في المبلغ
                var oldAmount = _orderToEdit.TotalAmount;
                var newAmount = OrderItems.Sum(item => item.TotalAmount);
                var amountDifference = newAmount - oldAmount;

                // تحديث بيانات الطلب الأساسية
                _orderToEdit.OrderDate = DpOrderDate.SelectedDate ?? DateTime.Now;
                _orderToEdit.TotalAmount = newAmount;
                _orderToEdit.RemainingAmount = newAmount - _orderToEdit.PaidAmount;
                _orderToEdit.Status = GetOrderStatus();
                _orderToEdit.LastModifiedDate = EgyptTimeHelper.Now;

                // إضافة منتجات الطلب الجديدة
                foreach (var item in OrderItems)
                {
                    var orderItem = new OrderItem
                    {
                        OrderId = _orderToEdit.OrderId,
                        ProductName = item.FabricType.Length > 100 ? item.FabricType.Substring(0, 100) : item.FabricType,
                        Color = !string.IsNullOrEmpty(item.Color) && item.Color.Length > 50 ? item.Color.Substring(0, 50) : item.Color,
                        Quantity = item.Quantity,
                        UnitPrice = item.PricePerMeter,
                        TotalPrice = item.TotalAmount,
                        Specifications = !string.IsNullOrEmpty(item.Notes) && item.Notes.Length > 200 ? item.Notes.Substring(0, 200) : item.Notes,
                        CreatedDate = EgyptTimeHelper.Now
                    };
                    await context.OrderItems.AddAsync(orderItem);
                }

                // تحديث رصيد العميل
                var customer = await context.Customers.FindAsync(_customer.CustomerId);
                if (customer != null)
                {
                    customer.Balance += amountDifference;
                    customer.LastModifiedDate = EgyptTimeHelper.Now;
                    context.Customers.Update(customer);
                }

                context.CustomerOrders.Update(_orderToEdit);
                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                MessageBox.Show("✅ تم تحديث الطلب بنجاح!", "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                throw new Exception($"خطأ في تحديث الطلب: {ex.Message}", ex);
            }
        }

        private async Task CreateNewOrder()
        {
            // حساب الإجمالي الكلي للطلب
            var totalAmount = OrderItems.Sum(item => item.TotalAmount);

            // إنشاء طلبات منفصلة لكل منتج
            var savedOrders = new List<CustomerOrder>();

            foreach (var item in OrderItems)
            {
                // التأكد من حدود النصوص
                var fabricType = item.FabricType.Length > 100 ?
                    item.FabricType.Substring(0, 100) : item.FabricType;

                var color = !string.IsNullOrEmpty(item.Color) && item.Color.Length > 50 ?
                    item.Color.Substring(0, 50) : item.Color;

                var notes = !string.IsNullOrEmpty(item.Notes) && item.Notes.Length > 500 ?
                    item.Notes.Substring(0, 500) : item.Notes;

                var order = new CustomerOrder
                {
                    CustomerId = _customer.CustomerId,
                    OrderNumber = GenerateOrderNumber(),
                    OrderDate = DpOrderDate.SelectedDate ?? DateTime.Now,
                    FabricType = fabricType,
                    Color = color,
                    Quantity = item.Quantity,
                    PricePerMeter = item.PricePerMeter,
                    TotalAmount = item.TotalAmount,
                    PaidAmount = 0,
                    RemainingAmount = item.TotalAmount,
                    Status = GetOrderStatus(),
                    Notes = notes,
                    CreatedDate = EgyptTimeHelper.Now,
                    LastModifiedDate = EgyptTimeHelper.Now
                };

                savedOrders.Add(order);
            }

            // حفظ أول طلب في NewOrder للمرجع
            NewOrder = savedOrders.FirstOrDefault();

            // حفظ الطلب في قاعدة البيانات مع Transaction
            using var context = new FactoryDbContext();
            using var transaction = await context.Database.BeginTransactionAsync();

            try
            {
                // التحقق من وجود العميل في قاعدة البيانات
                var customer = await context.Customers.FindAsync(_customer.CustomerId);
                if (customer == null)
                {
                    throw new Exception($"العميل غير موجود في قاعدة البيانات (ID: {_customer.CustomerId})");
                }

                // إضافة جميع الطلبات
                foreach (var order in savedOrders)
                {
                    await context.CustomerOrders.AddAsync(order);
                }

                // تحديث رصيد العميل (زيادة المديونية)
                customer.Balance += totalAmount;
                customer.LastModifiedDate = EgyptTimeHelper.Now;
                context.Customers.Update(customer);

                // حفظ التغييرات
                await context.SaveChangesAsync();
                await transaction.CommitAsync();

                OrderSaved = true;

                // رسالة نجاح
                var itemsCount = OrderItems.Count;
                var ordersCount = savedOrders.Count;

                MessageBox.Show(
                    $"✅ تم حفظ الطلب بنجاح!\n\n" +
                    $"📦 عدد المنتجات: {itemsCount} منتج\n" +
                    $"📋 عدد الطلبات المنشأة: {ordersCount} طلب\n" +
                    $"👤 العميل: {_customer.Name}\n" +
                    $"💰 إجمالي المبلغ: {totalAmount:N2} ج.م\n" +
                    $"📊 رصيد العميل الجديد: {(_customer.Balance + totalAmount):N2} ج.م\n\n" +
                    $"🔗 تم ربط جميع الطلبات بالعميل تلقائياً",
                    "تم الحفظ بنجاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                DialogResult = true;
                Close();
            }
            catch (Exception dbEx)
            {
                await transaction.RollbackAsync();
                throw new Exception($"خطأ في حفظ البيانات: {dbEx.Message}", dbEx);
            }
        }

        private bool ValidateInput()
        {
            // التحقق من وجود منتجات في الطلب
            if (OrderItems.Count == 0)
            {
                MessageBox.Show("⚠️ يرجى إضافة منتج واحد على الأقل للطلب", "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // التحقق من التاريخ
            if (!DpOrderDate.SelectedDate.HasValue)
            {
                MessageBox.Show("⚠️ يرجى اختيار تاريخ الطلب", "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                DpOrderDate.Focus();
                return false;
            }

            // التحقق من الحالة
            if (CmbStatus.SelectedItem == null)
            {
                MessageBox.Show("⚠️ يرجى اختيار حالة الطلب", "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbStatus.Focus();
                return false;
            }

            // التحقق من صحة جميع المنتجات
            for (int i = 0; i < OrderItems.Count; i++)
            {
                var item = OrderItems[i];
                if (!item.IsValid(out string errorMessage))
                {
                    MessageBox.Show($"⚠️ خطأ في المنتج رقم {i + 1}: {errorMessage}", "بيانات غير صحيحة", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }

            return true;
        }

        private bool CheckCreditLimit(decimal orderAmount)
        {
            try
            {
                // حساب المديونية الجديدة بعد إضافة الطلب
                var newBalance = _customer.Balance + orderAmount; // Balance موجب للمديونية

                // التحقق من تجاوز حد الائتمان
                if (newBalance > _customer.CreditLimit)
                {
                    var currentDebt = _customer.Balance;
                    var availableCredit = _customer.CreditLimit - currentDebt;

                    var result = MessageBox.Show(
                        $"⚠️ تحذير: تجاوز حد الائتمان!\n\n" +
                        $"💰 مبلغ الطلب: {orderAmount:N2} ج.م\n" +
                        $"📊 المديونية الحالية: {currentDebt:N2} ج.م\n" +
                        $"🏦 حد الائتمان: {_customer.CreditLimit:N2} ج.م\n" +
                        $"✅ الائتمان المتاح: {availableCredit:N2} ج.م\n" +
                        $"❌ المديونية بعد الطلب: {newBalance:N2} ج.م\n\n" +
                        $"هل تريد المتابعة رغم تجاوز حد الائتمان؟",
                        "تجاوز حد الائتمان",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    return result == MessageBoxResult.Yes;
                }

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التحقق من حد الائتمان: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private OrderStatus GetOrderStatus()
        {
            if (CmbStatus.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag != null)
            {
                return Enum.Parse<OrderStatus>(selectedItem.Tag.ToString() ?? "Pending");
            }
            return OrderStatus.Pending;
        }

        private string GetStatusText(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.Pending => "جديد",
                OrderStatus.InProgress => "قيد التنفيذ",
                OrderStatus.Completed => "مكتمل",
                OrderStatus.Cancelled => "ملغي",
                _ => "غير محدد"
            };
        }
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "❓ هل أنت متأكد من إلغاء إضافة الطلب؟\n\n⚠️ سيتم فقدان جميع البيانات المدخلة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                DialogResult = false;
                Close();
            }
        }

        // ===== دوال مساعدة =====

        // ===== إدارة المنتجات =====

        /// <summary>
        /// إضافة منتج جديد
        /// </summary>
        private void BtnAddItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var newItem = CreateItemFromCurrentInputs();
                string errorMessage = "بيانات غير صحيحة";

                if (newItem != null && newItem.IsValid(out errorMessage))
                {
                    OrderItems.Add(newItem);
                    ClearCurrentInputs();
                    UpdateOrderSummary();

                    MessageBox.Show("✅ تم إضافة المنتج بنجاح!", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    if (newItem == null)
                        errorMessage = "يرجى التأكد من صحة البيانات المدخلة";

                    MessageBox.Show($"⚠️ {errorMessage}", "بيانات غير صحيحة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل منتج موجود
        /// </summary>
        private void BtnEditItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.DataContext is OrderProductViewModel item)
                {
                    _currentEditingItem = item;
                    LoadItemToInputs(item);
                    BtnAddItem.Content = "💾 حفظ التعديل";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المنتج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف منتج
        /// </summary>
        private void BtnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.DataContext is OrderProductViewModel item)
                {
                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف المنتج '{item.FabricType}'؟",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        OrderItems.Remove(item);
                        UpdateOrderSummary();

                        MessageBox.Show("✅ تم حذف المنتج بنجاح!", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المنتج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء منتج من المدخلات الحالية
        /// </summary>
        private OrderProductViewModel? CreateItemFromCurrentInputs()
        {
            try
            {
                if (!decimal.TryParse(TxtQuantity.Text, out decimal quantity) || quantity <= 0)
                    return null;

                if (!decimal.TryParse(TxtUnitPrice.Text, out decimal price) || price <= 0)
                    return null;

                var fabricType = TxtDescription.Text?.Trim();
                if (string.IsNullOrWhiteSpace(fabricType))
                    return null;

                var color = CmbColor.SelectedItem != null ?
                    ((ComboBoxItem)CmbColor.SelectedItem).Content.ToString() : null;

                var item = new OrderProductViewModel
                {
                    FabricType = fabricType,
                    Color = color,
                    Quantity = quantity,
                    PricePerMeter = price,
                    Notes = TxtNotes.Text?.Trim()
                };

                return item;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تحميل بيانات المنتج إلى المدخلات
        /// </summary>
        private void LoadItemToInputs(OrderProductViewModel item)
        {
            TxtDescription.Text = item.FabricType;
            TxtQuantity.Text = item.Quantity.ToString();
            TxtUnitPrice.Text = item.PricePerMeter.ToString();
            TxtNotes.Text = item.Notes;

            // تعيين اللون
            if (!string.IsNullOrEmpty(item.Color))
            {
                foreach (ComboBoxItem colorItem in CmbColor.Items)
                {
                    if (colorItem.Content.ToString() == item.Color)
                    {
                        CmbColor.SelectedItem = colorItem;
                        break;
                    }
                }
            }
            else
            {
                CmbColor.SelectedIndex = -1;
            }
        }

        /// <summary>
        /// مسح المدخلات الحالية
        /// </summary>
        private void ClearCurrentInputs()
        {
            TxtDescription.Clear();
            TxtQuantity.Clear();
            TxtUnitPrice.Clear();
            TxtNotes.Clear();
            CmbColor.SelectedIndex = -1;
            _currentEditingItem = null;
            BtnAddItem.Content = "➕ إضافة منتج";
        }

        /// <summary>
        /// تحديث ملخص الطلب
        /// </summary>
        private void UpdateOrderSummary()
        {
            try
            {
                var itemsCount = OrderItems?.Count ?? 0;
                var grandTotal = OrderItems?.Sum(item => item.TotalAmount) ?? 0;

                if (TxtItemsCount != null)
                    TxtItemsCount.Text = $"{itemsCount} منتج";
                if (TxtGrandTotal != null)
                    TxtGrandTotal.Text = $"{grandTotal:N2} ج.م";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تحديث ملخص الطلب: {ex.Message}");
                if (TxtItemsCount != null)
                    TxtItemsCount.Text = "0 منتج";
                if (TxtGrandTotal != null)
                    TxtGrandTotal.Text = "0.00 ج.م";
            }
        }

        protected override void OnClosed(EventArgs e)
        {
            _context?.Dispose();
            base.OnClosed(e);
        }
    }
}
