using System;
using System.IO;
using System.Windows;
using FactoryManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace FactoryManagementSystem.Utilities
{
    /// <summary>
    /// أداة تنظيف وإعادة تهيئة قاعدة البيانات
    /// </summary>
    public static class DatabaseCleaner
    {
        /// <summary>
        /// تنظيف قاعدة البيانات بالكامل وإعادة إنشائها
        /// </summary>
        public static bool CleanAndResetDatabase()
        {
            try
            {
                var result = MessageBox.Show(
                    "هذا الإجراء سيحذف جميع البيانات الموجودة في قاعدة البيانات ويعيد إنشاءها من جديد.\n\n" +
                    "هل أنت متأكد من أنك تريد المتابعة؟\n\n" +
                    "تأكد من عمل نسخة احتياطية أولاً إذا كنت تحتاج البيانات الحالية.",
                    "تأكيد تنظيف قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                {
                    return false;
                }

                // إنشاء نسخة احتياطية تلقائية قبل التنظيف
                CreateAutoBackup();

                // حذف قاعدة البيانات الحالية
                DeleteCurrentDatabase();

                // إعادة إنشاء قاعدة البيانات
                RecreateDatabase();

                // إضافة بيانات أساسية
                SeedBasicData();

                MessageBox.Show(
                    "تم تنظيف قاعدة البيانات وإعادة إنشائها بنجاح!\n\n" +
                    "تم إنشاء نسخة احتياطية تلقائية في مجلد Backups.",
                    "تم التنظيف بنجاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تنظيف قاعدة البيانات:\n\n{ex.Message}",
                    "خطأ في التنظيف",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية تلقائية قبل التنظيف
        /// </summary>
        private static void CreateAutoBackup()
        {
            try
            {
                var dbPath = GetDatabasePath();
                if (!File.Exists(dbPath))
                {
                    return; // لا توجد قاعدة بيانات للنسخ الاحتياطي
                }

                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                Directory.CreateDirectory(backupDir);

                var backupFileName = $"AutoBackup_BeforeClean_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                var backupPath = Path.Combine(backupDir, backupFileName);

                File.Copy(dbPath, backupPath, true);
            }
            catch (Exception ex)
            {
                // لا نوقف العملية بسبب فشل النسخ الاحتياطي
                Console.WriteLine($"فشل في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف قاعدة البيانات الحالية
        /// </summary>
        private static void DeleteCurrentDatabase()
        {
            var dbPath = GetDatabasePath();
            if (File.Exists(dbPath))
            {
                // إغلاق جميع الاتصالات
                GC.Collect();
                GC.WaitForPendingFinalizers();

                File.Delete(dbPath);
            }

            // حذف ملفات SQLite الإضافية إن وجدت
            var walFile = dbPath + "-wal";
            var shmFile = dbPath + "-shm";

            if (File.Exists(walFile))
                File.Delete(walFile);

            if (File.Exists(shmFile))
                File.Delete(shmFile);
        }

        /// <summary>
        /// إعادة إنشاء قاعدة البيانات
        /// </summary>
        private static void RecreateDatabase()
        {
            using var context = new FactoryDbContext();
            
            // حذف قاعدة البيانات إذا كانت موجودة
            context.Database.EnsureDeleted();
            
            // إنشاء قاعدة البيانات الجديدة
            context.Database.EnsureCreated();
            
            // تطبيق أي migrations معلقة
            context.Database.Migrate();
        }

        /// <summary>
        /// إضافة بيانات أساسية لقاعدة البيانات الجديدة
        /// </summary>
        private static void SeedBasicData()
        {
            using var context = new FactoryDbContext();

            // إضافة مستخدم افتراضي
            if (!context.Users.Any())
            {
                var defaultUser = new Models.User
                {
                    Username = "admin",
                    Password = "123", // كلمة مرور بسيطة للاختبار
                    Role = "Administrator",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                context.Users.Add(defaultUser);
            }

            // إضافة عميل تجريبي
            if (!context.Customers.Any())
            {
                var testCustomer = new Models.Customer
                {
                    Name = "عميل تجريبي",
                    Phone = "01000000000",
                    Address = "عنوان تجريبي",
                    City = "القاهرة",
                    Balance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                context.Customers.Add(testCustomer);
            }

            // إضافة مورد تجريبي
            if (!context.Suppliers.Any())
            {
                var testSupplier = new Models.Supplier
                {
                    Name = "مورد تجريبي",
                    CompanyName = "شركة تجريبية",
                    Phone = "01000000001",
                    Address = "عنوان تجريبي",
                    City = "الجيزة",
                    Balance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                context.Suppliers.Add(testSupplier);
            }

            // إضافة بيانات تجريبية أخرى حسب الحاجة
            // يمكن إضافة المزيد من البيانات الأساسية هنا

            context.SaveChanges();
        }

        /// <summary>
        /// الحصول على مسار قاعدة البيانات
        /// </summary>
        private static string GetDatabasePath()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            return Path.Combine(dataDirectory, "FactoryDbContext.db");
        }

        /// <summary>
        /// فحص حالة قاعدة البيانات
        /// </summary>
        public static bool CheckDatabaseHealth()
        {
            try
            {
                using var context = new FactoryDbContext();
                
                // محاولة الاتصال بقاعدة البيانات
                context.Database.CanConnect();
                
                // فحص وجود الجداول الأساسية
                var tablesExist = context.Users.Any() || 
                                context.Customers.Any() || 
                                context.Suppliers.Any();

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إصلاح مشاكل قاعدة البيانات البسيطة
        /// </summary>
        public static bool RepairDatabase()
        {
            try
            {
                using var context = new FactoryDbContext();
                
                // التأكد من وجود قاعدة البيانات
                context.Database.EnsureCreated();
                
                // تطبيق أي migrations معلقة
                context.Database.Migrate();
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"فشل في إصلاح قاعدة البيانات:\n{ex.Message}",
                    "خطأ في الإصلاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }
    }
}
