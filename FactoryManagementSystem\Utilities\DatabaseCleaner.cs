using System;
using System.IO;
using System.Windows;
using FactoryManagementSystem.Data;
using Microsoft.EntityFrameworkCore;

namespace FactoryManagementSystem.Utilities
{
    /// <summary>
    /// أداة تنظيف وإعادة تهيئة قاعدة البيانات
    /// </summary>
    public static class DatabaseCleaner
    {
        /// <summary>
        /// تنظيف قاعدة البيانات بالكامل وإعادة إنشائها
        /// </summary>
        public static bool CleanAndResetDatabase()
        {
            try
            {
                var result = MessageBox.Show(
                    "هذا الإجراء سيحذف جميع البيانات الموجودة في قاعدة البيانات ويعيد إنشاءها من جديد.\n\n" +
                    "هل أنت متأكد من أنك تريد المتابعة؟\n\n" +
                    "تأكد من عمل نسخة احتياطية أولاً إذا كنت تحتاج البيانات الحالية.",
                    "تأكيد تنظيف قاعدة البيانات",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                {
                    return false;
                }

                // إنشاء نسخة احتياطية تلقائية قبل التنظيف
                CreateAutoBackup();

                try
                {
                    // محاولة التنظيف المباشر
                    DeleteCurrentDatabase();
                    RecreateDatabase();
                    SeedBasicData();

                    MessageBox.Show(
                        "تم تنظيف قاعدة البيانات وإعادة إنشائها بنجاح!\n\n" +
                        "تم إنشاء نسخة احتياطية تلقائية في مجلد Backups.",
                        "تم التنظيف بنجاح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);

                    return true;
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("تعذر حذف الملف"))
                {
                    // إذا فشل الحذف بسبب الملفات المقفلة، اقتراح إعادة التشغيل
                    return HandleLockedFileCleanup();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تنظيف قاعدة البيانات:\n\n{ex.Message}",
                    "خطأ في التنظيف",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// معالجة تنظيف قاعدة البيانات عند وجود ملفات مقفلة
        /// </summary>
        private static bool HandleLockedFileCleanup()
        {
            var result = MessageBox.Show(
                "تعذر تنظيف قاعدة البيانات لأن الملفات مستخدمة حالياً.\n\n" +
                "يمكنك:\n" +
                "1. إغلاق التطبيق بالكامل وإعادة تشغيله\n" +
                "2. أو الضغط على 'نعم' لجدولة التنظيف عند إعادة التشغيل التالية\n\n" +
                "هل تريد جدولة التنظيف لإعادة التشغيل التالية؟",
                "ملفات قاعدة البيانات مقفلة",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                ScheduleCleanupOnRestart();

                MessageBox.Show(
                    "تم جدولة تنظيف قاعدة البيانات لإعادة التشغيل التالية.\n\n" +
                    "سيتم تنظيف قاعدة البيانات تلقائياً عند بدء التطبيق في المرة القادمة.\n\n" +
                    "يُنصح بإغلاق التطبيق الآن وإعادة تشغيله.",
                    "تم جدولة التنظيف",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                return true;
            }

            return false;
        }

        /// <summary>
        /// إنشاء نسخة احتياطية تلقائية قبل التنظيف
        /// </summary>
        private static void CreateAutoBackup()
        {
            try
            {
                var dbPath = GetDatabasePath();
                if (!File.Exists(dbPath))
                {
                    return; // لا توجد قاعدة بيانات للنسخ الاحتياطي
                }

                var backupDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");
                Directory.CreateDirectory(backupDir);

                var backupFileName = $"AutoBackup_BeforeClean_{DateTime.Now:yyyyMMdd_HHmmss}.db";
                var backupPath = Path.Combine(backupDir, backupFileName);

                File.Copy(dbPath, backupPath, true);
            }
            catch (Exception ex)
            {
                // لا نوقف العملية بسبب فشل النسخ الاحتياطي
                Console.WriteLine($"فشل في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف قاعدة البيانات الحالية مع معالجة مشكلة الملفات المقفلة
        /// </summary>
        private static void DeleteCurrentDatabase()
        {
            var dbPath = GetDatabasePath();

            // إغلاق جميع اتصالات قاعدة البيانات
            CloseAllDatabaseConnections();

            // محاولة حذف الملفات مع إعادة المحاولة
            DeleteFileWithRetry(dbPath);
            DeleteFileWithRetry(dbPath + "-wal");
            DeleteFileWithRetry(dbPath + "-shm");
        }

        /// <summary>
        /// إغلاق جميع اتصالات قاعدة البيانات
        /// </summary>
        private static void CloseAllDatabaseConnections()
        {
            try
            {
                // إجبار إغلاق جميع الاتصالات
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();

                // محاولة إغلاق اتصالات SQLite
                Microsoft.Data.Sqlite.SqliteConnection.ClearAllPools();

                // انتظار قصير للتأكد من إغلاق الاتصالات
                System.Threading.Thread.Sleep(500);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: مشكلة في إغلاق الاتصالات: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف ملف مع إعادة المحاولة في حالة كان مقفل
        /// </summary>
        private static void DeleteFileWithRetry(string filePath)
        {
            if (!File.Exists(filePath))
                return;

            int maxRetries = 5;
            int retryDelay = 1000; // 1 ثانية

            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    File.Delete(filePath);
                    return; // نجح الحذف
                }
                catch (IOException) when (i < maxRetries - 1)
                {
                    // الملف مقفل، انتظار وإعادة المحاولة
                    System.Threading.Thread.Sleep(retryDelay);

                    // إعادة محاولة إغلاق الاتصالات
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                }
                catch (UnauthorizedAccessException) when (i < maxRetries - 1)
                {
                    // مشكلة في الصلاحيات، انتظار وإعادة المحاولة
                    System.Threading.Thread.Sleep(retryDelay);
                }
            }

            // إذا فشلت جميع المحاولات، رمي استثناء مخصص
            throw new InvalidOperationException(
                $"تعذر حذف الملف {Path.GetFileName(filePath)}. " +
                "يرجى إغلاق التطبيق بالكامل وإعادة تشغيله، ثم المحاولة مرة أخرى.");
        }

        /// <summary>
        /// إعادة إنشاء قاعدة البيانات
        /// </summary>
        private static void RecreateDatabase()
        {
            using var context = new FactoryDbContext();
            
            // حذف قاعدة البيانات إذا كانت موجودة
            context.Database.EnsureDeleted();
            
            // إنشاء قاعدة البيانات الجديدة
            context.Database.EnsureCreated();
            
            // تطبيق أي migrations معلقة
            context.Database.Migrate();
        }

        /// <summary>
        /// إضافة بيانات أساسية لقاعدة البيانات الجديدة
        /// </summary>
        private static void SeedBasicData()
        {
            using var context = new FactoryDbContext();

            // إضافة مستخدم افتراضي
            if (!context.Users.Any())
            {
                var defaultUser = new Models.User
                {
                    Username = "admin",
                    Password = "123", // كلمة مرور بسيطة للاختبار
                    Role = "Administrator",
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                context.Users.Add(defaultUser);
            }

            // إضافة عميل تجريبي
            if (!context.Customers.Any())
            {
                var testCustomer = new Models.Customer
                {
                    Name = "عميل تجريبي",
                    Phone = "01000000000",
                    Address = "عنوان تجريبي",
                    City = "القاهرة",
                    Balance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                context.Customers.Add(testCustomer);
            }

            // إضافة مورد تجريبي
            if (!context.Suppliers.Any())
            {
                var testSupplier = new Models.Supplier
                {
                    Name = "مورد تجريبي",
                    CompanyName = "شركة تجريبية",
                    Phone = "01000000001",
                    Address = "عنوان تجريبي",
                    City = "الجيزة",
                    Balance = 0,
                    IsActive = true,
                    CreatedDate = DateTime.Now
                };
                context.Suppliers.Add(testSupplier);
            }

            // إضافة بيانات تجريبية أخرى حسب الحاجة
            // يمكن إضافة المزيد من البيانات الأساسية هنا

            context.SaveChanges();
        }

        /// <summary>
        /// الحصول على مسار قاعدة البيانات
        /// </summary>
        private static string GetDatabasePath()
        {
            var baseDirectory = AppDomain.CurrentDomain.BaseDirectory;
            var dataDirectory = Path.Combine(baseDirectory, "Data");
            return Path.Combine(dataDirectory, "FactoryDbContext.db");
        }

        /// <summary>
        /// فحص حالة قاعدة البيانات
        /// </summary>
        public static bool CheckDatabaseHealth()
        {
            try
            {
                using var context = new FactoryDbContext();
                
                // محاولة الاتصال بقاعدة البيانات
                context.Database.CanConnect();
                
                // فحص وجود الجداول الأساسية
                var tablesExist = context.Users.Any() || 
                                context.Customers.Any() || 
                                context.Suppliers.Any();

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// إصلاح مشاكل قاعدة البيانات البسيطة
        /// </summary>
        public static bool RepairDatabase()
        {
            try
            {
                using var context = new FactoryDbContext();
                
                // التأكد من وجود قاعدة البيانات
                context.Database.EnsureCreated();
                
                // تطبيق أي migrations معلقة
                context.Database.Migrate();
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"فشل في إصلاح قاعدة البيانات:\n{ex.Message}",
                    "خطأ في الإصلاح",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// جدولة تنظيف قاعدة البيانات عند إعادة التشغيل
        /// </summary>
        private static void ScheduleCleanupOnRestart()
        {
            try
            {
                var flagFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "cleanup_scheduled.flag");
                File.WriteAllText(flagFile, DateTime.Now.ToString());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"فشل في جدولة التنظيف: {ex.Message}");
            }
        }

        /// <summary>
        /// فحص ما إذا كان هناك تنظيف مجدول
        /// </summary>
        public static bool IsCleanupScheduled()
        {
            var flagFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "cleanup_scheduled.flag");
            return File.Exists(flagFile);
        }

        /// <summary>
        /// تنفيذ التنظيف المجدول
        /// </summary>
        public static bool ExecuteScheduledCleanup()
        {
            try
            {
                var flagFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "cleanup_scheduled.flag");

                if (!File.Exists(flagFile))
                    return false;

                // إنشاء نسخة احتياطية
                CreateAutoBackup();

                // حذف قاعدة البيانات
                DeleteCurrentDatabase();

                // إعادة إنشاء قاعدة البيانات
                RecreateDatabase();

                // إضافة بيانات أساسية
                SeedBasicData();

                // حذف ملف الجدولة
                File.Delete(flagFile);

                MessageBox.Show(
                    "تم تنظيف قاعدة البيانات المجدول بنجاح!\n\n" +
                    "المستخدم الافتراضي: admin\n" +
                    "كلمة السر: 123",
                    "تنظيف مجدول مكتمل",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"فشل في تنفيذ التنظيف المجدول:\n{ex.Message}",
                    "خطأ في التنظيف المجدول",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إلغاء التنظيف المجدول
        /// </summary>
        public static void CancelScheduledCleanup()
        {
            try
            {
                var flagFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "cleanup_scheduled.flag");
                if (File.Exists(flagFile))
                {
                    File.Delete(flagFile);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"فشل في إلغاء التنظيف المجدول: {ex.Message}");
            }
        }
    }
}
