<Page x:Class="FactoryManagementSystem.Views.ChecksPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:local="clr-namespace:FactoryManagementSystem.Views"
      xmlns:helpers="clr-namespace:FactoryManagementSystem.Helpers"
      mc:Ignorable="d" 
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="ChecksPage"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            <local:PendingCheckVisibilityConverter x:Key="PendingCheckVisibilityConverter"/>
            <local:IncomingCheckVisibilityConverter x:Key="IncomingCheckVisibilityConverter"/>
            <helpers:CheckRowStyleSelector x:Key="CheckRowStyleSelector"/>
            
            <!-- Row Styles for different check statuses -->
            <Style x:Key="PendingRowStyle" TargetType="DataGridRow">
                <Setter Property="Background" Value="#FFF3CD"/>
                <Setter Property="BorderBrush" Value="#FFEAA7"/>
            </Style>
            
            <Style x:Key="CashedRowStyle" TargetType="DataGridRow">
                <Setter Property="Background" Value="#D1ECF1"/>
                <Setter Property="BorderBrush" Value="#BEE5EB"/>
            </Style>
            
            <Style x:Key="OutgoingRowStyle" TargetType="DataGridRow">
                <Setter Property="Background" Value="#F8D7DA"/>
                <Setter Property="BorderBrush" Value="#F5C6CB"/>
            </Style>
            
            <Style x:Key="ReturnedRowStyle" TargetType="DataGridRow">
                <Setter Property="Background" Value="#E2E3E5"/>
                <Setter Property="BorderBrush" Value="#D6D8DB"/>
            </Style>
            
            <Style x:Key="CancelledRowStyle" TargetType="DataGridRow">
                <Setter Property="Background" Value="#F8F9FA"/>
                <Setter Property="BorderBrush" Value="#DEE2E6"/>
            </Style>
        </ResourceDictionary>
    </Page.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <StackPanel Margin="20">
            <TextBlock Text="إدارة الشيكات" Style="{StaticResource HeaderText}"/>
            
            <!-- Enhanced Search and Filter Controls -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,16">
                <TextBlock Text="البحث:" Style="{StaticResource LabelText}" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <TextBox x:Name="TxtSearch" Width="200" Height="32" Style="{StaticResource InputField}" 
                         TextChanged="TxtSearch_TextChanged" Margin="0,0,20,0"/>
                
                <TextBlock Text="الحالة:" Style="{StaticResource LabelText}" VerticalAlignment="Center" Margin="0,0,8,0"/>
                <ComboBox x:Name="CmbStatusFilter" Width="150" Margin="8,0,0,0" SelectionChanged="CmbStatusFilter_SelectionChanged">
                    <ComboBoxItem Content="الكل" Tag="All" IsSelected="True"/>
                    <ComboBoxItem Content="مستحق (معلق)" Tag="Pending"/>
                    <ComboBoxItem Content="تم الصرف" Tag="Cashed"/>
                    <ComboBoxItem Content="صادر" Tag="Outgoing"/>
                    <ComboBoxItem Content="مرتد" Tag="Returned"/>
                    <ComboBoxItem Content="ملغي" Tag="Cancelled"/>
                </ComboBox>
                
                <TextBlock Text="النوع:" Style="{StaticResource LabelText}" VerticalAlignment="Center" Margin="20,0,8,0"/>
                <ComboBox x:Name="CmbTypeFilter" Width="120" Margin="8,0,0,0" SelectionChanged="CmbTypeFilter_SelectionChanged">
                    <ComboBoxItem Content="الكل" Tag="All" IsSelected="True"/>
                    <ComboBoxItem Content="وارد" Tag="Incoming"/>
                    <ComboBoxItem Content="صادر" Tag="Outgoing"/>
                </ComboBox>
                
                <Button Content="تحديث" Style="{StaticResource PrimaryButton}" Margin="20,0,0,0" Click="BtnRefresh_Click"/>
                <Button Content="طباعة" Style="{StaticResource PrimaryButton}" Margin="8,0,0,0" Click="BtnPrint_Click"/>
                <Button Content="تقرير شامل" Style="{StaticResource PrimaryButton}" Margin="8,0,0,0" Click="BtnComprehensiveReport_Click" Background="#9B59B6"/>
            </StackPanel>
            
            <!-- جدول الشيكات الاحترافي -->
            <DataGrid x:Name="DgChecks"
                      AutoGenerateColumns="False"
                      IsReadOnly="True"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      SelectionMode="Single"
                      Background="White"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="#FAFBFC"
                      RowHeight="80"
                      FontSize="14"
                      BorderThickness="1"
                      BorderBrush="#E5E7EB"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Auto"
                      ColumnHeaderHeight="50"
                      Height="450"
                      Margin="0,0,0,0"
                      MinWidth="1400">

                <!-- تنسيق رأس الجدول الاحترافي -->
                <DataGrid.ColumnHeaderStyle>
                    <Style TargetType="DataGridColumnHeader">
                        <Setter Property="Background" Value="#2D2D2D"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Padding" Value="15,15"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                        <Setter Property="BorderBrush" Value="#404040"/>
                        <Setter Property="BorderThickness" Value="0,0,1,0"/>
                        <Setter Property="Height" Value="50"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="DataGridColumnHeader">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            CornerRadius="0">
                                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                                          Margin="{TemplateBinding Padding}"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </DataGrid.ColumnHeaderStyle>

                <!-- تنسيق الصفوف الاحترافي -->
                <DataGrid.RowStyle>
                    <Style TargetType="DataGridRow">
                        <Setter Property="Background" Value="White"/>
                        <Setter Property="BorderBrush" Value="#E5E7EB"/>
                        <Setter Property="BorderThickness" Value="0,0,0,1"/>
                        <Setter Property="Height" Value="80"/>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F8F9FA"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="#E3F2FD"/>
                                <Setter Property="BorderBrush" Value="#2196F3"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.RowStyle>

                <!-- تنسيق الخلايا الموحد -->
                <DataGrid.CellStyle>
                    <Style TargetType="DataGridCell">
                        <Setter Property="Padding" Value="20,18"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="DataGridCell">
                                    <Border Background="{TemplateBinding Background}"
                                            BorderBrush="{TemplateBinding BorderBrush}"
                                            BorderThickness="{TemplateBinding BorderThickness}"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </DataGrid.CellStyle>
                <DataGrid.Columns>
                    <!-- رقم الشيك -->
                    <DataGridTextColumn Header="رقم الشيك" Binding="{Binding CheckNumber}" Width="150">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- البنك -->
                    <DataGridTextColumn Header="البنك" Binding="{Binding BankName}" Width="180">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- تاريخ الشيك -->
                    <DataGridTextColumn Header="تاريخ الشيك" Binding="{Binding CheckDate, StringFormat=yyyy/MM/dd}" Width="140">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Segoe UI"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- تاريخ الاستحقاق -->
                    <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat=yyyy/MM/dd}" Width="150">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="FontFamily" Value="Segoe UI"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- المبلغ -->
                    <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat='{}{0:N0} ج.م'}" Width="140">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="FontWeight" Value="SemiBold"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2E7D32"/>
                                <Setter Property="FontFamily" Value="Segoe UI"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- النوع -->
                    <DataGridTextColumn Header="النوع" Binding="{Binding TypeText}" Width="160">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- العميل/المورد -->
                    <DataGridTextColumn Header="العميل/المورد" Binding="{Binding PartyName}" Width="200">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <!-- الحالة -->
                    <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="130">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="FontWeight" Value="Medium"/>
                                <Setter Property="FontSize" Value="14"/>
                                <Setter Property="Foreground" Value="#2D2D2D"/>
                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                <Setter Property="VerticalAlignment" Value="Center"/>
                                <Setter Property="TextAlignment" Value="Center"/>
                                <Setter Property="Padding" Value="10,5"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    <!-- الإجراءات -->
                    <DataGridTemplateColumn Header="الإجراءات" Width="650">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal"
                                            HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="5,4">

                                    <!-- زر الصرف -->
                                    <Button Content="صرف"
                                            Click="BtnCashCheck_Click"
                                            Visibility="{Binding Status, Converter={StaticResource PendingCheckVisibilityConverter}}"
                                            Style="{StaticResource AnimatedButton}"
                                            Margin="3,0"
                                            Padding="12,6"
                                            MinWidth="85"
                                            MinHeight="42"
                                            Height="Auto"
                                            Background="#E8F5E8"
                                            BorderBrush="#4CAF50"
                                            BorderThickness="1"
                                            Foreground="#1B5E20"
                                            FontSize="12"
                                            FontWeight="Bold"
                                            FontFamily="Segoe UI"
                                            VerticalAlignment="Center"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            ToolTip="صرف الشيك"/>

                                    <!-- زر دفع لمورد -->
                                    <Button Content="دفع لمورد"
                                            Click="BtnPayToSupplier_Click"
                                            Visibility="{Binding Type, Converter={StaticResource IncomingCheckVisibilityConverter}}"
                                            Style="{StaticResource AnimatedButton}"
                                            Margin="3,0"
                                            Padding="12,6"
                                            MinWidth="85"
                                            MinHeight="42"
                                            Height="Auto"
                                            Background="#FFF8E1"
                                            BorderBrush="#FFC107"
                                            BorderThickness="1"
                                            Foreground="#E65100"
                                            FontSize="12"
                                            FontWeight="Bold"
                                            FontFamily="Segoe UI"
                                            VerticalAlignment="Center"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            ToolTip="دفع لمورد"/>

                                    <!-- زر الإرجاع -->
                                    <Button Content="إرجاع"
                                            Click="BtnReturnCheck_Click"
                                            Visibility="{Binding Status, Converter={StaticResource PendingCheckVisibilityConverter}}"
                                            Style="{StaticResource AnimatedButton}"
                                            Margin="3,0"
                                            Padding="12,6"
                                            MinWidth="85"
                                            MinHeight="42"
                                            Height="Auto"
                                            Background="#FFEBEE"
                                            BorderBrush="#F44336"
                                            BorderThickness="1"
                                            Foreground="#B71C1C"
                                            FontSize="12"
                                            FontWeight="Bold"
                                            FontFamily="Segoe UI"
                                            VerticalAlignment="Center"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            ToolTip="إرجاع الشيك"/>



                                    <!-- زر عرض الطرف -->
                                    <Button Content="عرض الطرف"
                                            Click="BtnViewParty_Click"
                                            Style="{StaticResource AnimatedButton}"
                                            Margin="3,0"
                                            Padding="12,6"
                                            MinWidth="85"
                                            MinHeight="42"
                                            Height="Auto"
                                            Background="#F3E5F5"
                                            BorderBrush="#9C27B0"
                                            BorderThickness="1"
                                            Foreground="#6A1B9A"
                                            FontSize="12"
                                            FontWeight="Bold"
                                            FontFamily="Segoe UI"
                                            VerticalAlignment="Center"
                                            HorizontalContentAlignment="Center"
                                            VerticalContentAlignment="Center"
                                            ToolTip="عرض الطرف"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
            
            <!-- إحصائيات الشيكات المحسنة -->
            <Border Background="#2D2D2D"
                    CornerRadius="8"
                    Margin="0,20,0,0"
                    Padding="20,15">
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Center">

                    <!-- الشيكات المعلقة -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                        <TextBlock Text="إجمالي الشيكات المعلقة: "
                                   FontSize="14"
                                   FontWeight="Medium"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                        <TextBlock x:Name="TxtPendingTotal"
                                   Text="0"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FF6B6B"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- الشيكات المقبوضة -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                        <TextBlock Text="إجمالي الشيكات المقبوضة: "
                                   FontSize="14"
                                   FontWeight="Medium"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                        <TextBlock x:Name="TxtCashedTotal"
                                   Text="0"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#4ECDC4"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- الشيكات الصادرة -->
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="إجمالي الشيكات الصادرة: "
                                   FontSize="14"
                                   FontWeight="Medium"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                        <TextBlock x:Name="TxtOutgoingTotal"
                                   Text="0"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="#FFE66D"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </StackPanel>
    </Grid>
</Page>
