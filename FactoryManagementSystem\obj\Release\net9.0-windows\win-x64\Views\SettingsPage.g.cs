﻿#pragma checksum "..\..\..\..\..\Views\SettingsPage.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E1F9547D75800E945FF50130A9D62973B9912FA6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace FactoryManagementSystem.Views {
    
    
    /// <summary>
    /// SettingsPage
    /// </summary>
    public partial class SettingsPage : System.Windows.Controls.Page, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgUsers;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddUser;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEditUser;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteUser;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbPasswordUser;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox TxtNewPassword;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.PasswordBox TxtConfirmPassword;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnChangePassword;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\..\Views\SettingsPage.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCleanDatabase;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/FactoryManagementSystem;V3.0.0.0;component/views/settingspage.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\SettingsPage.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.5.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DgUsers = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 2:
            this.BtnAddUser = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\..\Views\SettingsPage.xaml"
            this.BtnAddUser.Click += new System.Windows.RoutedEventHandler(this.BtnAddUser_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnEditUser = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\..\Views\SettingsPage.xaml"
            this.BtnEditUser.Click += new System.Windows.RoutedEventHandler(this.BtnEditUser_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnDeleteUser = ((System.Windows.Controls.Button)(target));
            
            #line 144 "..\..\..\..\..\Views\SettingsPage.xaml"
            this.BtnDeleteUser.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteUser_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CmbPasswordUser = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.TxtNewPassword = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 7:
            this.TxtConfirmPassword = ((System.Windows.Controls.PasswordBox)(target));
            return;
            case 8:
            this.BtnChangePassword = ((System.Windows.Controls.Button)(target));
            
            #line 213 "..\..\..\..\..\Views\SettingsPage.xaml"
            this.BtnChangePassword.Click += new System.Windows.RoutedEventHandler(this.BtnChangePassword_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnCleanDatabase = ((System.Windows.Controls.Button)(target));
            
            #line 242 "..\..\..\..\..\Views\SettingsPage.xaml"
            this.BtnCleanDatabase.Click += new System.Windows.RoutedEventHandler(this.BtnCleanDatabase_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

