# دليل تنظيف قاعدة البيانات

## المشكلة
إذا ظهرت لك رسالة خطأ مثل:
```
Object reference not set to an instance of an object
```
أو
```
The process cannot access the file because it is being used by another process
```

## الحلول المتاحة

### الحل الأول: التنظيف من داخل التطبيق
1. شغل التطبيق
2. اذهب إلى صفحة **الإعدادات** (أيقونة الترس)
3. اضغط على زر **🗑️ تنظيف قاعدة البيانات**
4. اقرأ رسالة التحذير واضغط **نعم**
5. إذا ظهرت رسالة "ملفات مقفلة"، اضغط **نعم** لجدولة التنظيف
6. أغلق التطبيق بالكامل وأعد تشغيله

### الحل الثاني: التنظيف التلقائي عند بدء التطبيق
1. شغل التطبيق
2. إذا كان هناك تنظيف مجدول، ستظهر رسالة
3. اضغط **نعم** لتنفيذ التنظيف
4. انتظر حتى اكتمال العملية

### الحل الثالث: الحل اليدوي (للطوارئ)
1. أغلق التطبيق بالكامل
2. اذهب إلى مجلد التطبيق
3. احذف مجلد `Data` بالكامل
4. شغل التطبيق مرة أخرى

## البيانات الافتراضية بعد التنظيف

### المستخدم الافتراضي:
- **اسم المستخدم**: `admin`
- **كلمة السر**: `123`

### البيانات التجريبية:
- عميل تجريبي
- مورد تجريبي

## النسخ الاحتياطية
- يتم إنشاء نسخة احتياطية تلقائياً قبل التنظيف
- تُحفظ في مجلد `Backups`
- اسم الملف: `AutoBackup_BeforeClean_YYYYMMDD_HHMMSS.db`

## نصائح مهمة
1. **تأكد من إغلاق التطبيق بالكامل** قبل التنظيف اليدوي
2. **احتفظ بنسخة احتياطية** من بياناتك المهمة
3. **أعد تشغيل التطبيق** بعد التنظيف
4. **لا تحذف مجلد Backups** - يحتوي على النسخ الاحتياطية

## استكشاف الأخطاء

### إذا استمرت المشكلة:
1. تأكد من إغلاق جميع نوافذ التطبيق
2. تحقق من Task Manager وأغلق أي عمليات متعلقة بالتطبيق
3. أعد تشغيل الكمبيوتر إذا لزم الأمر
4. جرب الحل اليدوي

### إذا فقدت البيانات:
1. ابحث في مجلد `Backups`
2. انسخ ملف النسخة الاحتياطية إلى مجلد `Data`
3. أعد تسميته إلى `FactoryDbContext.db`
4. شغل التطبيق

## الدعم
إذا استمرت المشاكل، تواصل مع فريق الدعم مع إرفاق:
- رسالة الخطأ كاملة
- خطوات إعادة إنتاج المشكلة
- ملفات السجل إن وجدت
