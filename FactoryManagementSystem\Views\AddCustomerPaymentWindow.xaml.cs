using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;

namespace FactoryManagementSystem.Views
{
    public partial class AddCustomerPaymentWindow : Window
    {
        private readonly Customer _customer = null!;
        public CustomerPayment? NewPayment { get; private set; }
        private bool _isEditMode = false;
        private CustomerPayment? _paymentToEdit;

        public AddCustomerPaymentWindow(Customer customer)
        {
            try
            {
                InitializeComponent();
                _customer = customer ?? throw new ArgumentNullException(nameof(customer));
                InitializeWindow();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء نافذة الدفعة: {ex.Message}\n\nتفاصيل الخطأ: {ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public AddCustomerPaymentWindow(Customer customer, CustomerPayment paymentToEdit) : this(customer)
        {
            _isEditMode = true;
            _paymentToEdit = paymentToEdit;
            FillPaymentFields();
        }

        private void InitializeWindow()
        {
            try
            {
                // عرض معلومات العميل
                TxtCustomerName.Text = _customer.Name;
                TxtCustomerBalance.Text = $"{_customer.Balance:N2} ج.م";

                // حساب إجمالي المديونية
                using var context = new FactoryDbContext();
                var totalDebt = context.CustomerOrders
                    .Where(o => o.CustomerId == _customer.CustomerId)
                    .Sum(o => o.RemainingAmount);
                TxtTotalDebt.Text = $"{totalDebt:N2} ج.م";

                // تحميل الطلبات غير المدفوعة
                LoadUnpaidOrders();

                // إنشاء رقم دفعة تلقائي (إلا في حالة التعديل)
                if (!_isEditMode)
                {
                    var paymentNumber = $"PAY-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
                    TxtPaymentNumber.Text = paymentNumber;
                }

                // تعيين التاريخ الحالي
                DpPaymentDate.SelectedDate = DateTime.Now;
                DpCheckDate.SelectedDate = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadUnpaidOrders()
        {
            try
            {
                using var context = new FactoryDbContext();
                var unpaidOrders = context.CustomerOrders
                    .Where(o => o.CustomerId == _customer.CustomerId && o.RemainingAmount > 0)
                    .OrderByDescending(o => o.OrderDate)
                    .ToList();

                CmbOrder.Items.Clear();

                // إضافة خيار "دفعة عامة"
                CmbOrder.Items.Add(new ComboBoxItem
                {
                    Content = "دفعة عامة (غير مرتبطة بطلب محدد)",
                    Tag = null
                });

                // إضافة الطلبات غير المدفوعة
                foreach (var order in unpaidOrders)
                {
                    var item = new ComboBoxItem
                    {
                        Content = $"طلب {order.OrderNumber} - {order.FabricType} - متبقي: {order.RemainingAmount:N2} ج.م",
                        Tag = order
                    };
                    CmbOrder.Items.Add(item);
                }

                // اختيار أول طلب افتراضياً
                if (CmbOrder.Items.Count > 1)
                    CmbOrder.SelectedIndex = 1;
                else
                    CmbOrder.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الطلبات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbOrder_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbOrder.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag is CustomerOrder order)
            {
                // ملء المبلغ بالمبلغ المتبقي للطلب
                TxtAmount.Text = order.RemainingAmount.ToString("F2");
                
                // تحديث معلومات العميل
                TxtTotalDebt.Text = $"{order.RemainingAmount:N2} ج.م";
            }
            else if (CmbOrder.SelectedItem is ComboBoxItem generalItem && generalItem.Tag == null)
            {
                // إذا كان اختيار "دفعة عامة"، اترك المبلغ فارغاً
                TxtAmount.Text = "";
            }
        }

        private void CmbPaymentMethod_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (PnlCheckDetails == null || PnlWalletDetails == null)
                return;
            if (CmbPaymentMethod.SelectedItem is ComboBoxItem selectedItem)
            {
                var method = selectedItem.Tag?.ToString();

                // إخفاء جميع التفاصيل أولاً
                PnlCheckDetails.Visibility = Visibility.Collapsed;
                PnlWalletDetails.Visibility = Visibility.Collapsed;

                // إظهار التفاصيل المناسبة
                switch (method)
                {
                    case "Check":
                        PnlCheckDetails.Visibility = Visibility.Visible;
                        break;
                    case "VodafoneCash":
                    case "InstaPay":
                    case "Orange":
                    case "Etisalat":
                        PnlWalletDetails.Visibility = Visibility.Visible;
                        break;
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                var amount = decimal.Parse(TxtAmount.Text);
                var paymentMethod = GetSelectedPaymentMethod();

                // الحصول على الطلب المختار
                CustomerOrder? selectedOrder = null;
                if (CmbOrder.SelectedItem is ComboBoxItem selectedItem && selectedItem.Tag is CustomerOrder order)
                {
                    selectedOrder = order;
                }

                // إنشاء الدفعة
                NewPayment = new CustomerPayment
                {
                    CustomerId = _customer.CustomerId,
                    OrderId = selectedOrder?.OrderId,
                    PaymentNumber = TxtPaymentNumber.Text.Trim(),
                    PaymentDate = DpPaymentDate.SelectedDate ?? DateTime.Now,
                    Amount = amount,
                    PaymentMethod = paymentMethod,
                    CheckNumber = GetPaymentReference(paymentMethod),
                    BankName = GetPaymentDetails(paymentMethod),
                    CheckDate = paymentMethod == PaymentMethod.Check ? DpCheckDate.SelectedDate : null,
                    Notes = BuildPaymentNotes(paymentMethod)
                };

                // عرض تأكيد
                var result = MessageBox.Show(
                    $"💰 تأكيد تسجيل الدفعة:\n\n" +
                    $"👤 العميل: {_customer.Name}\n" +
                    $"💵 المبلغ: {amount:N2} ج.م\n" +
                    $"💳 طريقة الدفع: {GetPaymentMethodText(paymentMethod)}\n" +
                    $"📅 التاريخ: {NewPayment.PaymentDate:yyyy/MM/dd}\n\n" +
                    $"✅ سيتم:\n" +
                    $"• إضافة الدفعة لحساب العميل\n" +
                    $"• إضافة المبلغ للخزينة\n" +
                    $"• تحديث رصيد العميل\n" +
                    $"• تحديث الطلب المرتبط (إن وجد)\n\n" +
                    $"هل تريد المتابعة؟",
                    "تأكيد الدفعة",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SavePaymentAndUpdateCash();
                    DialogResult = true;
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الدفعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtAmount.Text))
            {
                MessageBox.Show("⚠️ يرجى إدخال المبلغ", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("⚠️ يرجى إدخال مبلغ صحيح أكبر من صفر", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmount.Focus();
                return false;
            }

            if (DpPaymentDate.SelectedDate == null)
            {
                MessageBox.Show("⚠️ يرجى اختيار تاريخ الدفعة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                DpPaymentDate.Focus();
                return false;
            }

            // التحقق من بيانات الشيك إذا كانت طريقة الدفع شيك (إجباري)
            var paymentMethod = GetSelectedPaymentMethod();
            if (paymentMethod == PaymentMethod.Check)
            {
                if (string.IsNullOrWhiteSpace(TxtCheckNumber.Text))
                {
                    MessageBox.Show("⚠️ يرجى إدخال رقم الشيك", "خطأ في بيانات الشيك", MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtCheckNumber.Focus();
                    return false;
                }

                if (string.IsNullOrWhiteSpace(TxtBankName.Text))
                {
                    MessageBox.Show("⚠️ يرجى إدخال اسم البنك", "خطأ في بيانات الشيك", MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtBankName.Focus();
                    return false;
                }

                if (DpCheckDate.SelectedDate == null)
                {
                    MessageBox.Show("⚠️ يرجى اختيار تاريخ الشيك", "خطأ في بيانات الشيك", MessageBoxButton.OK, MessageBoxImage.Warning);
                    DpCheckDate.Focus();
                    return false;
                }
            }

            // التحقق من بيانات المحفظة الإلكترونية (اختياري)
            if (paymentMethod == PaymentMethod.VodafoneCash || paymentMethod == PaymentMethod.InstaPay ||
                paymentMethod == PaymentMethod.Orange || paymentMethod == PaymentMethod.Etisalat)
            {
                // التحقق من صيغة رقم الهاتف إذا تم إدخاله
                var phone = TxtWalletPhone.Text.Trim();
                if (!string.IsNullOrWhiteSpace(phone))
                {
                    if (!phone.StartsWith("01") || phone.Length != 11)
                    {
                        MessageBox.Show("⚠️ يرجى إدخال رقم هاتف صحيح (01xxxxxxxxx) أو اتركه فارغاً", "خطأ في بيانات المحفظة", MessageBoxButton.OK, MessageBoxImage.Warning);
                        TxtWalletPhone.Focus();
                        return false;
                    }
                }
            }

            return true;
        }

        private PaymentMethod GetSelectedPaymentMethod()
        {
            if (CmbPaymentMethod.SelectedItem is ComboBoxItem selectedItem)
            {
                return selectedItem.Tag?.ToString() switch
                {
                    "Cash" => PaymentMethod.Cash,
                    "Check" => PaymentMethod.Check,
                    "BankTransfer" => PaymentMethod.BankTransfer,
                    "CreditCard" => PaymentMethod.CreditCard,
                    "VodafoneCash" => PaymentMethod.VodafoneCash,
                    "InstaPay" => PaymentMethod.InstaPay,
                    "Orange" => PaymentMethod.Orange,
                    "Etisalat" => PaymentMethod.Etisalat,
                    _ => PaymentMethod.Cash
                };
            }
            return PaymentMethod.Cash;
        }

        private string GetPaymentMethodText(PaymentMethod method)
        {
            return method switch
            {
                PaymentMethod.Cash => "نقدي",
                PaymentMethod.Check => "شيك",
                PaymentMethod.BankTransfer => "تحويل بنكي",
                PaymentMethod.CreditCard => "بطاقة ائتمان",
                PaymentMethod.VodafoneCash => "فودافون كاش",
                PaymentMethod.InstaPay => "انستا باي",
                PaymentMethod.Orange => "أورانج موني",
                PaymentMethod.Etisalat => "اتصالات كاش",
                _ => "نقدي"
            };
        }

        private string? GetPaymentReference(PaymentMethod method)
        {
            return method switch
            {
                PaymentMethod.Check => TxtCheckNumber.Text.Trim(),
                PaymentMethod.VodafoneCash or PaymentMethod.InstaPay or PaymentMethod.Orange or PaymentMethod.Etisalat
                    => TxtTransactionRef.Text.Trim(),
                _ => null
            };
        }

        private string? GetPaymentDetails(PaymentMethod method)
        {
            return method switch
            {
                PaymentMethod.Check => TxtBankName.Text.Trim(),
                PaymentMethod.VodafoneCash or PaymentMethod.InstaPay or PaymentMethod.Orange or PaymentMethod.Etisalat
                    => TxtWalletPhone.Text.Trim(),
                _ => null
            };
        }

        private string BuildPaymentNotes(PaymentMethod method)
        {
            var notes = TxtNotes.Text.Trim();
            var methodText = GetPaymentMethodText(method);

            var additionalInfo = method switch
            {
                PaymentMethod.Check => BuildCheckInfo(),
                PaymentMethod.VodafoneCash or PaymentMethod.InstaPay or PaymentMethod.Orange or PaymentMethod.Etisalat
                    => BuildWalletInfo(),
                _ => ""
            };

            return string.IsNullOrEmpty(additionalInfo)
                ? $"طريقة الدفع: {methodText}" + (string.IsNullOrEmpty(notes) ? "" : $" - {notes}")
                : $"طريقة الدفع: {methodText} - {additionalInfo}" + (string.IsNullOrEmpty(notes) ? "" : $" - {notes}");
        }

        private string BuildCheckInfo()
        {
            var checkNumber = TxtCheckNumber.Text.Trim();
            var bankName = TxtBankName.Text.Trim();

            var parts = new List<string>();
            if (!string.IsNullOrEmpty(checkNumber)) parts.Add($"شيك رقم: {checkNumber}");
            if (!string.IsNullOrEmpty(bankName)) parts.Add($"بنك: {bankName}");

            return string.Join(" - ", parts);
        }

        private string BuildWalletInfo()
        {
            var phone = TxtWalletPhone.Text.Trim();
            var transactionRef = TxtTransactionRef.Text.Trim();

            var parts = new List<string>();
            if (!string.IsNullOrEmpty(phone)) parts.Add($"رقم الهاتف: {phone}");
            if (!string.IsNullOrEmpty(transactionRef)) parts.Add($"رقم العملية: {transactionRef}");

            return string.Join(" - ", parts);
        }

        private string GenerateTransactionNumber(FactoryDbContext context)
        {
            var today = EgyptTimeHelper.Today;
            var prefix = $"TR-{today:yyyyMMdd}-";
            var last = context.CashTransactions
                .Where(t => t.TransactionNumber.StartsWith(prefix))
                .OrderByDescending(t => t.TransactionNumber)
                .FirstOrDefault();
            if (last == null)
                return prefix + "001";
            var lastNum = int.Parse(last.TransactionNumber.Substring(prefix.Length));
            return prefix + (lastNum + 1).ToString("D3");
        }

        private void SavePaymentAndUpdateCash()
        {
            using var context = new FactoryDbContext();
            using var transaction = context.Database.BeginTransaction();

            try
            {
                // 1. حفظ الدفعة
                context.CustomerPayments.Add(NewPayment!);

                // 2. إضافة حركة للخزينة (قبض) - بدون تحديث رصيد العميل لأننا سنحدثه يدوياً
                var cashTransaction = new CashTransaction
                {
                    TransactionNumber = GenerateTransactionNumber(context),
                    TransactionDate = EgyptTimeHelper.Now,
                    Type = TransactionType.Income,
                    Amount = NewPayment?.Amount ?? 0,
                    Category = TransactionCategory.CustomerPayment,
                    Description = $"دفعة من العميل: {_customer.Name}",
                    CustomerId = _customer.CustomerId,
                    RelatedPartyName = _customer.Name,
                    ReferenceNumber = NewPayment?.PaymentNumber ?? "",
                    CreatedDate = EgyptTimeHelper.Now
                };
                context.CashTransactions.Add(cashTransaction);

                // 3. إذا كانت الدفعة بشيك، أنشئ سجل شيك جديد
                if (NewPayment?.PaymentMethod == PaymentMethod.Check)
                {
                    var check = new Check
                    {
                        CheckNumber = NewPayment.CheckNumber ?? string.Empty,
                        CheckDate = NewPayment.PaymentDate, // تاريخ إنشاء الشيك (تاريخ الدفعة)
                        DueDate = NewPayment.CheckDate ?? NewPayment.PaymentDate.AddDays(30), // تاريخ الاستحقاق
                        Amount = NewPayment.Amount,
                        BankName = NewPayment.BankName ?? string.Empty,
                        Type = CheckType.Incoming,
                        Status = CheckStatus.Pending,
                        CustomerId = _customer.CustomerId,
                        PayeeName = _customer.Name,
                        Notes = $"شيك مستلم من عميل عبر دفعة رقم: {NewPayment.PaymentNumber}",
                        CreatedDate = DateTime.Now
                    };
                    context.Checks.Add(check);
                }

                // 4. تحديث رصيد العميل (تقليل المديونية)
                // رصيد العميل = إجمالي طلباته - إجمالي مدفوعاته
                _customer.Balance -= NewPayment?.Amount ?? 0; // تقليل المديونية
                _customer.LastModifiedDate = EgyptTimeHelper.Now;
                context.Customers.Update(_customer);

                // 5. تحديث الطلب إذا كان مرتبط بطلب محدد
                if (NewPayment?.OrderId.HasValue == true)
                {
                    var order = context.CustomerOrders.Find(NewPayment.OrderId.Value);
                    if (order != null)
                    {
                        order.PaidAmount += NewPayment.Amount;
                        order.RemainingAmount -= NewPayment.Amount;
                        if (order.RemainingAmount <= 0)
                        {
                            order.Status = OrderStatus.Completed;
                            order.RemainingAmount = 0;
                        }
                        else if (order.PaidAmount > 0)
                        {
                            order.Status = OrderStatus.PartiallyPaid;
                        }
                        order.LastModifiedDate = DateTime.Now;
                        context.CustomerOrders.Update(order);
                    }
                }

                // حفظ التغييرات
                context.SaveChanges();
                transaction.Commit();

                MessageBox.Show(
                    $"🎉 تم حفظ الدفعة بنجاح! ✅\n\n" +
                    $"📋 رقم الدفعة: {NewPayment?.PaymentNumber ?? ""}\n" +
                    $"💵 المبلغ: {NewPayment?.Amount ?? 0:N2} ج.م\n" +
                    $"💳 طريقة الدفع: {GetPaymentMethodText(NewPayment?.PaymentMethod ?? PaymentMethod.Cash)}\n" +
                    $"🏦 تم إضافة المبلغ للخزينة\n" +
                    $"👤 تم تحديث رصيد العميل\n" +
                    $"📊 تم تحديث الطلب المرتبط (إن وجد)\n" +
                    (NewPayment?.PaymentMethod == PaymentMethod.Check ? "\n📄 تم تسجيل الشيك في صفحة الشيكات" : ""),
                    "نجح الحفظ",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                var errorMsg = ex.InnerException != null ? ex.InnerException.Message : ex.Message;
                MessageBox.Show($"خطأ في حفظ الدفعة: {errorMsg}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        private void FillPaymentFields()
        {
            if (_paymentToEdit == null) return;
            TxtPaymentNumber.Text = _paymentToEdit.PaymentNumber;
            DpPaymentDate.SelectedDate = _paymentToEdit.PaymentDate;
            TxtAmount.Text = _paymentToEdit.Amount.ToString("N2");
            TxtNotes.Text = _paymentToEdit.Notes;
            // تعيين طريقة الدفع
            foreach (ComboBoxItem item in CmbPaymentMethod.Items)
            {
                if (item.Tag?.ToString() == _paymentToEdit.PaymentMethod.ToString())
                {
                    CmbPaymentMethod.SelectedItem = item;
                    break;
                }
            }
            // تعيين الطلب المرتبط
            if (_paymentToEdit.OrderId.HasValue)
            {
                foreach (ComboBoxItem item in CmbOrder.Items)
                {
                    if (item.Tag is CustomerOrder order && order.OrderId == _paymentToEdit.OrderId)
                    {
                        CmbOrder.SelectedItem = item;
                        break;
                    }
                }
            }
            else
            {
                CmbOrder.SelectedIndex = 0; // دفعة عامة
            }
            // شيك أو محفظة
            if (_paymentToEdit.PaymentMethod == PaymentMethod.Check)
            {
                TxtCheckNumber.Text = _paymentToEdit.CheckNumber;
                TxtBankName.Text = _paymentToEdit.BankName;
                DpCheckDate.SelectedDate = _paymentToEdit.CheckDate;
            }
            else if (_paymentToEdit.PaymentMethod == PaymentMethod.VodafoneCash || _paymentToEdit.PaymentMethod == PaymentMethod.InstaPay || _paymentToEdit.PaymentMethod == PaymentMethod.Orange || _paymentToEdit.PaymentMethod == PaymentMethod.Etisalat)
            {
                TxtTransactionRef.Text = _paymentToEdit.CheckNumber ?? "";
                TxtWalletPhone.Text = _paymentToEdit.BankName ?? "";
            }
        }
    }
}
