<Window x:Class="FactoryManagementSystem.Views.AddEditEmployeeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:FactoryManagementSystem.Views"
        mc:Ignorable="d"
        Title="إضافة/تعديل عامل"
        Height="700" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <Border Style="{StaticResource Card}" Margin="20">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- العنوان -->
                    <TextBlock x:Name="TxtTitle" 
                             Text="إضافة عامل جديد" 
                             Style="{StaticResource HeaderText}" 
                             HorizontalAlignment="Center"
                             Margin="0,0,0,30"/>

                    <!-- نموذج البيانات -->
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- اسم العامل -->
                        <TextBlock Grid.Row="0" Grid.Column="0" 
                                 Text="اسم العامل *" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="3"
                               x:Name="TxtName" 
                               Style="{StaticResource ModernTextBox}" 
                               Margin="0,0,0,15"/>

                        <!-- رقم العامل -->
                        <TextBlock Grid.Row="1" Grid.Column="0"
                                 Text="رقم العامل"
                                 Style="{StaticResource BodyText}"
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="1" Grid.Column="1"
                               x:Name="TxtEmployeeNumber"
                               Style="{StaticResource ModernTextBox}"
                               IsReadOnly="True"
                               Background="#F5F5F5"
                               Margin="0,0,15,15"/>

                        <!-- الرقم القومي -->
                        <TextBlock Grid.Row="1" Grid.Column="2" 
                                 Text="الرقم القومي" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="1" Grid.Column="3" 
                               x:Name="TxtNationalId" 
                               Style="{StaticResource ModernTextBox}" 
                               Margin="0,0,0,15"/>

                        <!-- الهاتف -->
                        <TextBlock Grid.Row="2" Grid.Column="0" 
                                 Text="الهاتف" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="2" Grid.Column="1" 
                               x:Name="TxtPhone" 
                               Style="{StaticResource ModernTextBox}" 
                               Margin="0,0,15,15"/>

                        <!-- تاريخ الميلاد -->
                        <TextBlock Grid.Row="2" Grid.Column="2" 
                                 Text="تاريخ الميلاد" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <DatePicker Grid.Row="2" Grid.Column="3" 
                                  x:Name="DpBirthDate" 
                                  Margin="0,0,0,15"/>

                        <!-- العنوان -->
                        <TextBlock Grid.Row="3" Grid.Column="0" 
                                 Text="العنوان" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Top" 
                                 Margin="0,8,0,15"/>
                        <TextBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3"
                               x:Name="TxtAddress" 
                               Style="{StaticResource ModernTextBox}" 
                               Height="60"
                               TextWrapping="Wrap"
                               AcceptsReturn="True"
                               VerticalScrollBarVisibility="Auto"
                               Margin="0,0,0,15"/>

                        <!-- تاريخ التوظيف -->
                        <TextBlock Grid.Row="4" Grid.Column="0" 
                                 Text="تاريخ التوظيف *" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <DatePicker Grid.Row="4" Grid.Column="1" 
                                  x:Name="DpHireDate" 
                                  Margin="0,0,15,15"/>

                        <!-- المنصب -->
                        <TextBlock Grid.Row="4" Grid.Column="2"
                                 Text="المنصب"
                                 Style="{StaticResource BodyText}"
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="4" Grid.Column="3"
                               x:Name="TxtPosition"
                               Style="{StaticResource ModernTextBox}"
                               Margin="0,0,0,15"/>

                        <!-- القسم -->
                        <TextBlock Grid.Row="5" Grid.Column="0"
                                 Text="القسم"
                                 Style="{StaticResource BodyText}"
                                 VerticalAlignment="Center"
                                 Margin="0,0,0,15"/>
                        <ComboBox Grid.Row="5" Grid.Column="1"
                                x:Name="CmbDepartment"
                                Margin="0,0,15,15"
                                IsEditable="True"
                                SelectedIndex="0">
                            <ComboBoxItem Content="الإنتاج"/>
                            <ComboBoxItem Content="الجودة"/>
                            <ComboBoxItem Content="الصيانة"/>
                            <ComboBoxItem Content="المخازن"/>
                            <ComboBoxItem Content="الإدارة"/>
                            <ComboBoxItem Content="الأمن"/>
                            <ComboBoxItem Content="النظافة"/>
                        </ComboBox>

                        <!-- الراتب الأساسي -->
                        <TextBlock Grid.Row="5" Grid.Column="2" 
                                 Text="الراتب الأساسي *" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="5" Grid.Column="3" 
                               x:Name="TxtBasicSalary" 
                               Style="{StaticResource ModernTextBox}" 
                               Text="0"
                               Margin="0,0,0,15"/>

                        <!-- أجر الساعة الإضافية -->
                        <TextBlock Grid.Row="6" Grid.Column="0" 
                                 Text="أجر الساعة الإضافية" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="6" Grid.Column="1" 
                               x:Name="TxtHourlyRate" 
                               Style="{StaticResource ModernTextBox}" 
                               Text="0"
                               Margin="0,0,15,15"/>

                        <!-- ساعات العمل اليومية -->
                        <TextBlock Grid.Row="6" Grid.Column="2" 
                                 Text="ساعات العمل اليومية" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="6" Grid.Column="3" 
                               x:Name="TxtWorkingHoursPerDay" 
                               Style="{StaticResource ModernTextBox}" 
                               Text="8"
                               Margin="0,0,0,15"/>

                        <!-- أيام العمل الأسبوعية -->
                        <TextBlock Grid.Row="7" Grid.Column="0" 
                                 Text="أيام العمل الأسبوعية" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <TextBox Grid.Row="7" Grid.Column="1" 
                               x:Name="TxtWorkingDaysPerWeek" 
                               Style="{StaticResource ModernTextBox}" 
                               Text="6"
                               Margin="0,0,15,15"/>

                        <!-- نشط -->
                        <TextBlock Grid.Row="7" Grid.Column="2" 
                                 Text="نشط" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Center" 
                                 Margin="0,0,0,15"/>
                        <CheckBox Grid.Row="7" Grid.Column="3" 
                                x:Name="ChkIsActive" 
                                IsChecked="True"
                                VerticalAlignment="Center"
                                Margin="0,0,0,15"/>

                        <!-- ملاحظات -->
                        <TextBlock Grid.Row="8" Grid.Column="0" 
                                 Text="ملاحظات" 
                                 Style="{StaticResource BodyText}" 
                                 VerticalAlignment="Top" 
                                 Margin="0,8,0,15"/>
                        <TextBox Grid.Row="8" Grid.Column="1" Grid.ColumnSpan="3"
                               x:Name="TxtNotes" 
                               Style="{StaticResource ModernTextBox}" 
                               Height="80"
                               TextWrapping="Wrap"
                               AcceptsReturn="True"
                               VerticalScrollBarVisibility="Auto"
                               Margin="0,0,0,15"/>
                    </Grid>

                    <!-- أزرار الإجراءات -->
                    <StackPanel Orientation="Horizontal" 
                              HorizontalAlignment="Center" 
                              Margin="0,30,0,0">
                        <Button x:Name="BtnSave" 
                                Content="💾 حفظ" 
                                Style="{StaticResource PrimaryButton}" 
                                Margin="0,0,15,0"
                                Click="BtnSave_Click"/>
                        
                        <Button x:Name="BtnCancel" 
                                Content="❌ إلغاء" 
                                Background="{StaticResource SecondaryColor}"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="16,8"
                                FontWeight="Medium"
                                Cursor="Hand"
                                Click="BtnCancel_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="6"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center"
                                                        VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#475569"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</Window>
