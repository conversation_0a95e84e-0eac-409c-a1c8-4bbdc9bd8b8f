<Page x:Class="FactoryManagementSystem.Views.SettingsPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      mc:Ignorable="d"
      d:DesignHeight="600" d:DesignWidth="800"
      Title="الإعدادات"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Page.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <TextBlock Grid.Row="0" 
                       Text="⚙️ إعدادات النظام" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Foreground="#1F2937" 
                       Margin="0,0,0,30"/>

            <!-- المحتوى -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- إدارة المستخدمين -->
                <Border Grid.Column="0" Style="{StaticResource Card}" Margin="0,0,15,0">
                    <StackPanel>
                        <TextBlock Text="👥 إدارة المستخدمين" 
                                   FontSize="18" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,20"
                                   HorizontalAlignment="Center"/>

                        <!-- قائمة المستخدمين -->
                        <TextBlock Text="المستخدمون الحاليون:" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,10"/>

                        <DataGrid x:Name="DgUsers"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  IsReadOnly="True"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  Background="White"
                                  RowBackground="#F9FAFB"
                                  AlternatingRowBackground="#F3F4F6"
                                  BorderThickness="1"
                                  BorderBrush="#E5E7EB"
                                  FontSize="12"
                                  Height="200"
                                  Margin="0,0,0,20">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم المستخدم" Binding="{Binding Username}" Width="120"/>
                                <DataGridTextColumn Header="الاسم المعروض" Binding="{Binding DisplayName}" Width="120"/>
                                <DataGridTextColumn Header="الدور" Binding="{Binding Role}" Width="80"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding StatusText}" Width="60"/>
                                <DataGridTextColumn Header="آخر دخول" Binding="{Binding LastLoginText}" Width="*"/>
                            </DataGrid.Columns>
                        </DataGrid>

                        <!-- أزرار إدارة المستخدمين -->
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button x:Name="BtnAddUser" 
                                    Content="➕ إضافة مستخدم" 
                                    Background="#10B981"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Padding="15,8"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Cursor="Hand"
                                    Margin="0,0,10,0"
                                    Click="BtnAddUser_Click">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="6"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#059669"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>

                            <Button x:Name="BtnEditUser" 
                                    Content="✏️ تعديل مستخدم" 
                                    Background="#F59E0B"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Padding="15,8"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Cursor="Hand"
                                    Margin="0,0,10,0"
                                    Click="BtnEditUser_Click">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="6"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#D97706"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>

                            <Button x:Name="BtnDeleteUser" 
                                    Content="🗑️ حذف مستخدم" 
                                    Background="#EF4444"
                                    Foreground="White"
                                    BorderThickness="0"
                                    Padding="15,8"
                                    FontSize="12"
                                    FontWeight="Bold"
                                    Cursor="Hand"
                                    Click="BtnDeleteUser_Click">
                                <Button.Template>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                                CornerRadius="6"
                                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#DC2626"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Button.Template>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- تغيير كلمة السر -->
                <Border Grid.Column="1" Style="{StaticResource Card}" Margin="15,0,0,0">
                    <StackPanel>
                        <TextBlock Text="🔐 تغيير كلمة السر" 
                                   FontSize="18" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,20"
                                   HorizontalAlignment="Center"/>

                        <!-- اختيار المستخدم -->
                        <TextBlock Text="اختر المستخدم:" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,8"/>
                        <ComboBox x:Name="CmbPasswordUser" 
                                  DisplayMemberPath="DisplayName"
                                  SelectedValuePath="UserId"
                                  FontSize="14"
                                  Padding="10"
                                  Margin="0,0,0,20"/>

                        <!-- كلمة السر الجديدة -->
                        <TextBlock Text="كلمة السر الجديدة:" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,8"/>
                        <PasswordBox x:Name="TxtNewPassword" 
                                     FontSize="14"
                                     Padding="10"
                                     Margin="0,0,0,20"/>

                        <!-- تأكيد كلمة السر -->
                        <TextBlock Text="تأكيد كلمة السر:" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,8"/>
                        <PasswordBox x:Name="TxtConfirmPassword" 
                                     FontSize="14"
                                     Padding="10"
                                     Margin="0,0,0,30"/>

                        <!-- زر تغيير كلمة السر -->
                        <Button x:Name="BtnChangePassword" 
                                Content="🔄 تغيير كلمة السر" 
                                Background="#3B82F6"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="20,10"
                                FontSize="14"
                                FontWeight="Bold"
                                Cursor="Hand"
                                HorizontalAlignment="Center"
                                Click="BtnChangePassword_Click">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="8"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#2563EB"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <!-- زر تنظيف قاعدة البيانات -->
                        <Button x:Name="BtnCleanDatabase"
                                Content="🗑️ تنظيف قاعدة البيانات"
                                Background="#EF4444"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="20,10"
                                FontSize="14"
                                FontWeight="Bold"
                                Cursor="Hand"
                                HorizontalAlignment="Center"
                                Margin="0,20,0,0"
                                Click="BtnCleanDatabase_Click"
                                ToolTip="حذف جميع البيانات وإعادة إنشاء قاعدة البيانات">
                            <Button.Template>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}"
                                            CornerRadius="8"
                                            Padding="{TemplateBinding Padding}">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                    <ControlTemplate.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#DC2626"/>
                                        </Trigger>
                                    </ControlTemplate.Triggers>
                                </ControlTemplate>
                            </Button.Template>
                        </Button>

                        <!-- معلومات إضافية -->
                        <Border Background="#FEF3C7" 
                                BorderBrush="#F59E0B" 
                                BorderThickness="1" 
                                CornerRadius="6" 
                                Padding="15" 
                                Margin="0,30,0,0">
                            <StackPanel>
                                <TextBlock Text="💡 ملاحظات مهمة:" 
                                           FontWeight="Bold" 
                                           Foreground="#92400E" 
                                           Margin="0,0,0,10"/>
                                <TextBlock Text="• لا تحتاج لإدخال كلمة السر القديمة" 
                                           FontSize="12" 
                                           Foreground="#92400E" 
                                           Margin="0,0,0,5"/>
                                <TextBlock Text="• يمكن تغيير كلمة سر أي مستخدم مباشرة" 
                                           FontSize="12" 
                                           Foreground="#92400E" 
                                           Margin="0,0,0,5"/>
                                <TextBlock Text="• تأكد من حفظ كلمة السر الجديدة" 
                                           FontSize="12" 
                                           Foreground="#92400E"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Border>
            </Grid>
        </Grid>
    </ScrollViewer>
</Page>
