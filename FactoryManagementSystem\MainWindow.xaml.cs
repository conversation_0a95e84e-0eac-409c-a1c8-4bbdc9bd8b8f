﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Views;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;

namespace FactoryManagementSystem;

/// <summary>
/// الواجهة الرئيسية لنظام إدارة المصنع
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer _clockTimer = new();
    private string _currentUsername = "مستخدم";

    public MainWindow() : this("مستخدم")
    {
        // Constructor افتراضي للتوافق مع الكود القديم
    }

    public MainWindow(string username)
    {
        try
        {
            _currentUsername = username;
            InitializeComponent();

            // فحص وإصلاح قاعدة البيانات إذا لزم الأمر
            CheckAndRepairDatabase();

            // عرض اسم المستخدم في الواجهة
            UpdateUserDisplay();
        }
        catch (Exception ex)
        {
            MessageBox.Show(ex.ToString());
        }

        InitializeDatabase();
        LoadDashboard();

        // تهيئة الساعة بعد تحميل النافذة
        Loaded += MainWindow_Loaded;
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        InitializeClock();
    }

    private void InitializeClock()
    {
        // تحديث التاريخ والوقت فوراً
        UpdateDateTime();

        // إنشاء Timer لتحديث الوقت كل ثانية
        _clockTimer.Interval = TimeSpan.FromSeconds(1);
        _clockTimer.Tick += ClockTimer_Tick;
        _clockTimer.Start();
    }

    private void ClockTimer_Tick(object? sender, EventArgs e)
    {
        UpdateDateTime();
    }

    private void UpdateDateTime()
    {
        try
        {
            // التحقق من وجود العناصر قبل التحديث
            if (TxtCurrentDate != null && TxtCurrentTime != null)
            {
                // استخدام التوقيت المصري
                TxtCurrentDate.Text = EgyptTimeHelper.ToDateString();
                TxtCurrentTime.Text = EgyptTimeHelper.ToTimeString();
            }
        }
        catch (Exception ex)
        {
            // تجاهل الأخطاء في تحديث الوقت
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث الوقت: {ex.Message}");
        }
    }

    private async void InitializeDatabase()
    {
        try
        {
            using var context = new FactoryDbContext();

            // إنشاء قاعدة البيانات والجداول
            await context.Database.EnsureCreatedAsync();

            // إضافة بيانات تجريبية إذا كانت قاعدة البيانات فارغة
            await SeedDatabaseIfEmpty(context);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إنشاء قاعدة البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task SeedDatabaseIfEmpty(FactoryDbContext context)
    {
        try
        {
            // التحقق من وجود بيانات
            var hasCustomers = await context.Customers.AnyAsync();
            var hasSuppliers = await context.Suppliers.AnyAsync();
            var hasEmployees = await context.Employees.AnyAsync();

            if (!hasCustomers && !hasSuppliers && !hasEmployees)
            {
                // إضافة بيانات تجريبية
                await AddSampleData(context);
            }
        }
        catch (Exception ex)
        {
            // تجاهل الأخطاء في البيانات التجريبية
            System.Diagnostics.Debug.WriteLine($"خطأ في إضافة البيانات التجريبية: {ex.Message}");
        }
    }

    private async Task AddSampleData(FactoryDbContext context)
    {
        // إضافة عملاء تجريبيين
        var customers = new[]
        {
            new Customer { Name = "أحمد محمد", Phone = "01234567890", Address = "القاهرة", Balance = 1500 },
            new Customer { Name = "فاطمة علي", Phone = "01098765432", Address = "الجيزة", Balance = 2300 },
            new Customer { Name = "محمود حسن", Phone = "01156789012", Address = "الإسكندرية", Balance = 800 }
        };
        context.Customers.AddRange(customers);

        // إضافة موردين تجريبيين
        var suppliers = new[]
        {
            new Supplier { Name = "شركة النسيج المصرية", Phone = "0223456789", Address = "القاهرة", Balance = 5000 },
            new Supplier { Name = "مصنع الخيوط الحديث", Phone = "0212345678", Address = "الجيزة", Balance = 3200 },
            new Supplier { Name = "توريدات الأقمشة", Phone = "0234567890", Address = "الإسكندرية", Balance = 1800 }
        };
        context.Suppliers.AddRange(suppliers);

        // إضافة عمال تجريبيين
        var employees = new[]
        {
            new Employee { Name = "علي أحمد", EmployeeNumber = "EMP001", Phone = "01123456789", Position = "عامل خياطة", BasicSalary = 4500 },
            new Employee { Name = "سارة محمد", EmployeeNumber = "EMP002", Phone = "01087654321", Position = "عاملة تطريز", BasicSalary = 3600 },
            new Employee { Name = "حسام علي", EmployeeNumber = "EMP003", Phone = "01198765432", Position = "مشرف إنتاج", BasicSalary = 6000 }
        };
        context.Employees.AddRange(employees);

        await context.SaveChangesAsync();
    }

    private void LoadDashboard()
    {
        var dashboardPage = new DashboardPage();
        MainFrame.Navigate(dashboardPage);
        UpdatePageTitle("لوحة التحكم", "نظرة عامة على أداء المصنع");
        HighlightActiveButton(BtnDashboard);
    }

    private void UpdatePageTitle(string title, string subtitle)
    {
        TxtPageTitle.Text = title;
        TxtPageSubtitle.Text = subtitle;
    }

    private void HighlightActiveButton(Button activeButton)
    {
        // قائمة جميع أزرار القائمة الجانبية
        var allButtons = new[] { BtnDashboard, BtnCustomers, BtnSuppliers, BtnEmployees,
                                BtnOrders, BtnCash, BtnChecks, BtnReports, BtnBackup, BtnSettings };

        // إزالة النمط النشط من جميع الأزرار
        foreach (var button in allButtons)
        {
            button.Style = (Style)FindResource("SidebarButton");
        }

        // تطبيق النمط النشط على الزر المحدد
        activeButton.Style = (Style)FindResource("SidebarButtonActive");
    }

    private void BtnDashboard_Click(object sender, RoutedEventArgs e)
    {
        var dashboardPage = new DashboardPage();
        MainFrame.Navigate(dashboardPage);
        UpdatePageTitle("لوحة التحكم", "نظرة عامة على أداء المصنع");
        HighlightActiveButton(BtnDashboard);
    }

    private void BtnCustomers_Click(object sender, RoutedEventArgs e)
    {
        var customersPage = new CustomersPage();
        MainFrame.Navigate(customersPage);
        UpdatePageTitle("إدارة العملاء", "إدارة بيانات العملاء والطلبات");
        HighlightActiveButton(BtnCustomers);
    }

    private void BtnSuppliers_Click(object sender, RoutedEventArgs e)
    {
        var suppliersPage = new SuppliersPage();
        MainFrame.Navigate(suppliersPage);
        UpdatePageTitle("إدارة الموردين", "إدارة بيانات الموردين والمشتريات");
        HighlightActiveButton(BtnSuppliers);
    }

    private void BtnEmployees_Click(object sender, RoutedEventArgs e)
    {
        var employeesPage = new EmployeesPage();
        MainFrame.Navigate(employeesPage);
        UpdatePageTitle("إدارة العمال", "إدارة بيانات العمال والحضور والرواتب");
        HighlightActiveButton(BtnEmployees);
    }

    private void BtnOrders_Click(object sender, RoutedEventArgs e)
    {
        var ordersPage = new OrdersPage();
        MainFrame.Navigate(ordersPage);
        UpdatePageTitle("الطلبات والفواتير", "إدارة طلبات العملاء وإنشاء الفواتير");
        HighlightActiveButton(BtnOrders);
    }

    private void BtnCash_Click(object sender, RoutedEventArgs e)
    {
        var cashPage = new CashPage();
        MainFrame.Navigate(cashPage);
        UpdatePageTitle("إدارة الخزينة", "إدارة حركات القبض والصرف");
        HighlightActiveButton(BtnCash);
    }

    private void BtnChecks_Click(object sender, RoutedEventArgs e)
    {
        var checksPage = new ChecksPage();
        MainFrame.Navigate(checksPage);
        UpdatePageTitle("إدارة الشيكات", "إدارة الشيكات الواردة والصادرة");
        HighlightActiveButton(BtnChecks);
    }



    private void BtnReports_Click(object sender, RoutedEventArgs e)
    {
        var reportsPage = new ReportsPage();
        MainFrame.Navigate(reportsPage);
        UpdatePageTitle("التقارير", "تقارير شاملة عن أداء المصنع");
        HighlightActiveButton(BtnReports);
    }

    private void BtnBackup_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var backupPage = new BackupPage();
            MainFrame.Navigate(backupPage);
            UpdatePageTitle("النسخ الاحتياطي", "إدارة النسخ الاحتياطية لقاعدة البيانات");
            HighlightActiveButton(BtnBackup);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح صفحة النسخ الاحتياطي: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSettings_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var settingsPage = new SettingsPage();
            MainFrame.Navigate(settingsPage);
            UpdatePageTitle("الإعدادات", "إدارة إعدادات النظام والمستخدمين");
            HighlightActiveButton(BtnSettings);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح صفحة الإعدادات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateUserDisplay()
    {
        try
        {
            // البحث عن عنصر عرض اسم المستخدم في الواجهة
            if (FindName("TxtCurrentUser") is TextBlock txtCurrentUser)
            {
                txtCurrentUser.Text = _currentUsername;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"خطأ في تحديث عرض المستخدم: {ex.Message}");
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        // إيقاف Timer عند إغلاق النافذة
        _clockTimer?.Stop();
        base.OnClosed(e);
    }

    /// <summary>
    /// فحص وإصلاح قاعدة البيانات عند بدء التطبيق
    /// </summary>
    private void CheckAndRepairDatabase()
    {
        try
        {
            // فحص حالة قاعدة البيانات
            if (!Utilities.DatabaseCleaner.CheckDatabaseHealth())
            {
                // محاولة إصلاح قاعدة البيانات
                bool repaired = Utilities.DatabaseCleaner.RepairDatabase();

                if (!repaired)
                {
                    // إذا فشل الإصلاح، اقتراح التنظيف
                    var result = MessageBox.Show(
                        "تم اكتشاف مشكلة في قاعدة البيانات ولم يتم إصلاحها تلقائياً.\n\n" +
                        "هل تريد تنظيف قاعدة البيانات وإعادة إنشائها؟\n" +
                        "(سيتم حذف جميع البيانات الحالية)",
                        "مشكلة في قاعدة البيانات",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Warning);

                    if (result == MessageBoxResult.Yes)
                    {
                        Utilities.DatabaseCleaner.CleanAndResetDatabase();
                    }
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show(
                $"خطأ في فحص قاعدة البيانات:\n{ex.Message}\n\n" +
                "يمكنك استخدام زر 'تنظيف قاعدة البيانات' من صفحة الإعدادات إذا استمرت المشكلة.",
                "خطأ في قاعدة البيانات",
                MessageBoxButton.OK,
                MessageBoxImage.Error);
        }
    }
}