using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.EntityFrameworkCore;
using FactoryManagementSystem.Data;
using FactoryManagementSystem.Models;
using FactoryManagementSystem.Helpers;

namespace FactoryManagementSystem.Views
{
    public partial class SettingsPage : Page
    {
        private readonly FactoryDbContext _context;
        public ObservableCollection<User> Users { get; set; }

        public SettingsPage()
        {
            InitializeComponent();
            _context = new FactoryDbContext();
            Users = new ObservableCollection<User>();

            Loaded += SettingsPage_Loaded;
        }

        private async void SettingsPage_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                await LoadUsers();
                await InitializeDefaultUser();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الإعدادات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task InitializeDefaultUser()
        {
            try
            {
                // التحقق من وجود مدير النظام الافتراضي
                var adminUser = await _context.Users.FirstOrDefaultAsync(u => u.Username == "مدير النظام");
                if (adminUser == null)
                {
                    // إنشاء مدير النظام الافتراضي
                    adminUser = new User
                    {
                        Username = "مدير النظام",
                        DisplayName = "مدير النظام",
                        Password = "admin123", // في التطبيق الحقيقي يجب تشفير كلمة السر
                        Role = "مدير",
                        IsActive = true,
                        CreatedDate = EgyptTimeHelper.Now
                    };

                    _context.Users.Add(adminUser);
                    await _context.SaveChangesAsync();
                    await LoadUsers(); // إعادة تحميل القائمة
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة المستخدم الافتراضي: {ex.Message}");
            }
        }

        private async Task LoadUsers()
        {
            try
            {
                var users = await _context.Users
                    .OrderBy(u => u.Username)
                    .ToListAsync();

                Users.Clear();
                foreach (var user in users)
                {
                    Users.Add(user);
                }

                // ربط البيانات
                DgUsers.ItemsSource = Users;
                CmbPasswordUser.ItemsSource = Users;

                // اختيار أول مستخدم في قائمة تغيير كلمة السر
                if (Users.Any())
                {
                    CmbPasswordUser.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المستخدمين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addUserWindow = new AddEditUserWindow();
                if (addUserWindow.ShowDialog() == true)
                {
                    // إعادة تحميل قائمة المستخدمين
                    _ = LoadUsers();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة مستخدم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgUsers.SelectedItem is User selectedUser)
                {
                    var editUserWindow = new AddEditUserWindow(selectedUser);
                    if (editUserWindow.ShowDialog() == true)
                    {
                        // إعادة تحميل قائمة المستخدمين
                        _ = LoadUsers();
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مستخدم للتعديل.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تعديل المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnDeleteUser_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DgUsers.SelectedItem is User selectedUser)
                {
                    // منع حذف مدير النظام
                    if (selectedUser.Username == "مدير النظام")
                    {
                        MessageBox.Show("لا يمكن حذف مدير النظام الافتراضي.", "تنبيه",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        return;
                    }

                    var result = MessageBox.Show(
                        $"هل أنت متأكد من حذف المستخدم '{selectedUser.DisplayName}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.",
                        "تأكيد الحذف",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _context.Users.Remove(selectedUser);
                        await _context.SaveChangesAsync();
                        await LoadUsers();

                        MessageBox.Show("تم حذف المستخدم بنجاح.", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار مستخدم للحذف.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المستخدم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnChangePassword_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من البيانات
                if (CmbPasswordUser.SelectedItem is not User selectedUser)
                {
                    MessageBox.Show("يرجى اختيار مستخدم.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (string.IsNullOrWhiteSpace(TxtNewPassword.Password))
                {
                    MessageBox.Show("يرجى إدخال كلمة السر الجديدة.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    TxtNewPassword.Focus();
                    return;
                }

                if (TxtNewPassword.Password != TxtConfirmPassword.Password)
                {
                    MessageBox.Show("كلمة السر وتأكيدها غير متطابقين.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    TxtConfirmPassword.Focus();
                    return;
                }

                if (TxtNewPassword.Password.Length < 3)
                {
                    MessageBox.Show("كلمة السر يجب أن تكون 3 أحرف على الأقل.", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    TxtNewPassword.Focus();
                    return;
                }

                // تحديث كلمة السر
                var userToUpdate = await _context.Users.FindAsync(selectedUser.UserId);
                if (userToUpdate != null)
                {
                    userToUpdate.Password = TxtNewPassword.Password; // في التطبيق الحقيقي يجب تشفير كلمة السر
                    await _context.SaveChangesAsync();

                    // مسح الحقول
                    TxtNewPassword.Clear();
                    TxtConfirmPassword.Clear();

                    MessageBox.Show($"تم تغيير كلمة سر المستخدم '{selectedUser.DisplayName}' بنجاح.", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المستخدم.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تغيير كلمة السر: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        ~SettingsPage()
        {
            _context?.Dispose();
        }

        /// <summary>
        /// تنظيف قاعدة البيانات وإعادة إنشائها
        /// </summary>
        private void BtnCleanDatabase_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // استخدام أداة تنظيف قاعدة البيانات
                bool success = Utilities.DatabaseCleaner.CleanAndResetDatabase();

                if (success)
                {
                    // إعادة تحميل قائمة المستخدمين
                    LoadUsers();

                    // إظهار رسالة نجاح إضافية
                    MessageBox.Show(
                        "تم تنظيف قاعدة البيانات بنجاح!\n\n" +
                        "تم إنشاء:\n" +
                        "• مستخدم افتراضي: admin (كلمة السر: 123)\n" +
                        "• عميل تجريبي\n" +
                        "• مورد تجريبي\n" +
                        "• منتج تجريبي\n\n" +
                        "يُنصح بإعادة تشغيل التطبيق.",
                        "تنظيف ناجح",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ أثناء تنظيف قاعدة البيانات:\n\n{ex.Message}",
                    "خطأ في التنظيف",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }
    }
}
