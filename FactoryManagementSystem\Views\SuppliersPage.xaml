<Page x:Class="FactoryManagementSystem.Views.SuppliersPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
      xmlns:local="clr-namespace:FactoryManagementSystem.Views"
      mc:Ignorable="d"
      d:DesignHeight="600" d:DesignWidth="1000"
      Title="SuppliersPage"
      FlowDirection="RightToLeft">

    <Page.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Resources/Styles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Page.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأدوات العلوي -->
        <Border Grid.Row="0" Style="{StaticResource Card}" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- مربع البحث والفلتر -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🔍" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBox x:Name="TxtSearch"
                             Style="{StaticResource ModernTextBox}"
                             Width="250"
                             TextChanged="TxtSearch_TextChanged"/>
                    <TextBlock Text="البحث في الموردين..."
                             Style="{StaticResource SecondaryText}"
                             VerticalAlignment="Center"
                             Margin="10,0,20,0"/>

                    <!-- فلتر الحالة -->
                    <TextBlock Text="الحالة:"
                             Style="{StaticResource BodyText}"
                             VerticalAlignment="Center"
                             Margin="0,0,10,0"/>
                    <ComboBox x:Name="CmbStatusFilter"
                            Width="120"
                            SelectedIndex="0"
                            SelectionChanged="CmbStatusFilter_SelectionChanged">
                        <ComboBoxItem Content="الكل"/>
                        <ComboBoxItem Content="نشط"/>
                        <ComboBoxItem Content="غير نشط"/>
                    </ComboBox>
                </StackPanel>

                <!-- أزرار الإجراءات -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button x:Name="BtnAddSupplier"
                            Content="➕ إضافة مورد جديد"
                            Style="{StaticResource PrimaryButton}"
                            Margin="0,0,10,0"
                            Click="BtnAddSupplier_Click"/>

                    <Button x:Name="BtnAddSampleData"
                            Content="📊 بيانات تجريبية"
                            Background="{StaticResource SecondaryColor}"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="16,8"
                            FontWeight="Medium"
                            Cursor="Hand"
                            Margin="0,0,10,0"
                            Click="BtnAddSampleData_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="6"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#475569"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <Button x:Name="BtnEditSupplier"
                            Content="✏️ تعديل"
                            Style="{StaticResource PrimaryButton}"
                            Margin="0,0,10,0"
                            IsEnabled="False"
                            Click="BtnEditSupplier_Click"/>

                    <Button x:Name="BtnDeleteSupplier"
                            Content="🗑️ حذف"
                            Background="{StaticResource DangerColor}"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="16,8"
                            FontWeight="Medium"
                            Cursor="Hand"
                            IsEnabled="False"
                            Click="BtnDeleteSupplier_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="6"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#DC2626"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- أزرار الطلبات والمدفوعات -->
                    <Button x:Name="BtnSupplierOrders"
                            Content="📦 طلبات الشراء"
                            Background="{StaticResource AccentColor}"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="16,8"
                            FontWeight="Medium"
                            Cursor="Hand"
                            Margin="10,0,0,0"
                            IsEnabled="False"
                            Click="BtnSupplierOrders_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="6"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#059669"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <Button x:Name="BtnSupplierPayments"
                            Content="💳 مدفوعات المورد"
                            Background="{StaticResource WarningColor}"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="16,8"
                            FontWeight="Medium"
                            Cursor="Hand"
                            Margin="10,0,0,0"
                            IsEnabled="False"
                            Click="BtnSupplierPayments_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="6"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#D97706"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <Button x:Name="BtnSupplierStatement"
                            Content="📄 كشف الحساب"
                            Background="{StaticResource AccentColor}"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="16,8"
                            FontWeight="Medium"
                            Cursor="Hand"
                            Margin="10,0,0,0"
                            IsEnabled="False"
                            Click="BtnSupplierStatement_Click">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}"
                                        CornerRadius="6"
                                        Padding="{TemplateBinding Padding}">
                                    <ContentPresenter HorizontalAlignment="Center"
                                                    VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#059669"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>



                    <Button x:Name="BtnRefreshSuppliers"
                            Content="🔄 تحديث"
                            Background="#6B7280"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="16,8"
                            FontWeight="Medium"
                            Cursor="Hand"
                            Margin="10,0,0,0"
                            Click="BtnRefreshSuppliers_Click">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <ScaleTransform ScaleX="1" ScaleY="1"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center"
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#4B5563"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="RenderTransform">
                                            <Setter.Value>
                                                <ScaleTransform ScaleX="1.05" ScaleY="1.05"/>
                                            </Setter.Value>
                                        </Setter>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- جدول الموردين -->
        <Border Grid.Row="1" Style="{StaticResource Card}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <TextBlock Grid.Row="0"
                         Text="قائمة الموردين"
                         Style="{StaticResource SubHeaderText}"
                         Margin="0,0,0,15"/>

                <!-- DataGrid -->
                <DataGrid Grid.Row="1"
                        x:Name="DgSuppliers"
                        Style="{StaticResource ModernDataGrid}"
                        SelectionChanged="DgSuppliers_SelectionChanged"
                        MouseDoubleClick="DgSuppliers_MouseDoubleClick">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم" Binding="{Binding SupplierId}" Width="80"/>
                        <DataGridTextColumn Header="اسم المورد" Binding="{Binding Name}" Width="180"/>
                        <DataGridTextColumn Header="اسم الشركة" Binding="{Binding CompanyName}" Width="180"/>
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                        <DataGridTextColumn Header="المدينة" Binding="{Binding City}" Width="100"/>
                        <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                        <DataGridTextColumn Header="تاريخ الإضافة" Binding="{Binding CreatedDate, StringFormat='yyyy/MM/dd'}" Width="120"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- معلومات إضافية -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,15,0,0">
                    <TextBlock Text="إجمالي الموردين: " Style="{StaticResource BodyText}"/>
                    <TextBlock x:Name="TxtTotalSuppliers" Text="0" Style="{StaticResource BodyText}" FontWeight="Bold"/>
                    <TextBlock Text=" | الموردين النشطين: " Style="{StaticResource BodyText}" Margin="20,0,0,0"/>
                    <TextBlock x:Name="TxtActiveSuppliers" Text="0" Style="{StaticResource BodyText}" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Page>
